"""
Модуль для загрузки данных криптовалют с публичных API без необходимости в ключах
Поддерживает Binance, CoinGecko, CryptoCompare
"""

import requests
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import os
import pickle
from pathlib import Path

class CryptoDataLoader:
    """Загрузчик данных криптовалют с публичных API"""
    
    def __init__(self, cache_dir: str = "crypto_data_cache"):
        """
        Инициализация загрузчика
        
        Args:
            cache_dir: Директория для кэширования данных
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Базовые URL для разных источников
        self.binance_base_url = "https://api.binance.us/api/v3"  # Используем основной API Binance
        self.coingecko_base_url = "https://api.coingecko.com/api/v3"
        self.cryptocompare_base_url = "https://min-api.cryptocompare.com/data/v2"
        
        # Таймфреймы Binance
        self.binance_intervals = {
            '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1h', '4h': '4h', '1d': '1d', '1w': '1w'
        }
        
    def get_binance_klines(self, symbol: str = "BTCUSDT", interval: str = "1h", 
                          limit: int = 1000, start_time: Optional[int] = None) -> pd.DataFrame:
        """
        Загрузка OHLCV данных с Binance
        
        Args:
            symbol: Торговая пара (по умолчанию BTCUSDT)
            interval: Временной интервал (1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w)
            limit: Количество свечей (максимум 1000 за запрос)
            start_time: Начальное время в миллисекундах (опционально)
            
        Returns:
            DataFrame с колонками: timestamp, open, high, low, close, volume
        """
        try:
            url = f"{self.binance_base_url}/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': min(limit, 1000)
            }
            
            if start_time:
                params['startTime'] = start_time
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Преобразуем в DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # Преобразуем типы данных
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['open'] = df['open'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(float)
            
            # Оставляем только нужные колонки
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Ошибка загрузки данных с Binance: {e}")
            return pd.DataFrame()
    
    def get_historical_data(self, symbol: str = "BTCUSDT", interval: str = "1h",
                           days: int = 30, force_reload: bool = False) -> pd.DataFrame:
        """
        Загрузка исторических данных за указанный период
        
        Args:
            symbol: Торговая пара
            interval: Временной интервал
            days: Количество дней истории
            force_reload: Принудительная перезагрузка (игнорировать кэш)
            
        Returns:
            DataFrame с историческими данными
        """
        # Проверяем кэш
        cache_file = self.cache_dir / f"{symbol}_{interval}_{days}days.pkl"
        
        if cache_file.exists() and not force_reload:
            # Для больших датасетов кэшируем на 24 часа, для малых - на 1 час
            cache_duration = 86400 if days > 30 else 3600  # 24 часа или 1 час
            cache_age = time.time() - cache_file.stat().st_mtime
            
            if cache_age < cache_duration:
                print(f"📂 Загрузка из кэша: {cache_file}")
                print(f"   Возраст кэша: {cache_age/3600:.1f} часов")
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        
        print(f"🌐 Загрузка данных {symbol} ({interval}) за {days} дней с Binance...")
        
        # Рассчитываем количество запросов
        intervals_per_day = {
            '1m': 1440, '5m': 288, '15m': 96, '30m': 48,
            '1h': 24, '4h': 6, '1d': 1, '1w': 0.14
        }
        
        candles_needed = int(days * intervals_per_day.get(interval, 24))
        requests_needed = (candles_needed // 1000) + 1
        
        print(f"   Требуется свечей: {candles_needed}")
        print(f"   Количество запросов: {requests_needed}")
        
        all_data = []
        
        # Начинаем с самого раннего времени и идем в прямом направлении
        start_time = int((time.time() - (days * 24 * 3600)) * 1000)  # days назад
        
        for i in range(requests_needed):
            print(f"   📥 Запрос {i+1}/{requests_needed}...")
            
            # Загружаем данные
            df_chunk = self.get_binance_klines(
                symbol=symbol,
                interval=interval,
                limit=1000,
                start_time=start_time
            )
            
            if df_chunk.empty:
                print(f"   ⚠️  Получен пустой ответ на запросе {i+1}")
                break
                
            all_data.append(df_chunk)
            
            # Обновляем start_time для следующего запроса
            if len(df_chunk) > 0:
                last_timestamp = df_chunk.iloc[-1]['timestamp']
                start_time = int(last_timestamp.timestamp() * 1000) + 1
                
                # Проверяем, не достигли ли мы текущего времени
                if start_time >= int(time.time() * 1000):
                    print(f"   ✅ Достигнуто текущее время")
                    break
            
            # Задержка между запросами (увеличена для больших загрузок)
            delay = 0.2 if requests_needed > 10 else 0.1
            time.sleep(delay)
            
            # Показываем прогресс
            loaded_candles = len(all_data) * 1000
            progress = min(100, (loaded_candles / candles_needed) * 100)
            print(f"   📊 Прогресс: {progress:.1f}% ({loaded_candles}/{candles_needed})")
        
        # Объединяем все данные
        if all_data:
            print("🔄 Объединение данных...")
            df = pd.concat(all_data, ignore_index=True)
            
            # Сортируем по времени и удаляем дубликаты
            df = df.sort_values('timestamp').drop_duplicates(subset=['timestamp']).reset_index(drop=True)
            
            # Ограничиваем количество данных точно по запрошенному периоду
            if len(df) > candles_needed:
                df = df.tail(candles_needed)
            
            print(f"📁 Сохранение в кэш...")
            # Сохраняем в кэш
            with open(cache_file, 'wb') as f:
                pickle.dump(df, f)
            
            print(f"✅ Успешно загружено {len(df)} свечей")
            print(f"   Период: {df['timestamp'].min()} - {df['timestamp'].max()}")
            
            return df
        
        print("❌ Не удалось загрузить данные")
        return pd.DataFrame()
    
    def load_large_dataset(self, symbol: str = "BTCUSDT", interval: str = "1h", 
                          days: int = 365) -> pd.DataFrame:
        """
        Специальный метод для загрузки больших датасетов (для обучения ML)
        
        Args:
            symbol: Торговая пара
            interval: Временной интервал
            days: Количество дней (рекомендуется 365+ для ML)
            
        Returns:
            DataFrame с большим объемом данных
        """
        print(f"🚀 ЗАГРУЗКА БОЛЬШОГО ДАТАСЕТА ДЛЯ ОБУЧЕНИЯ ML")
        print(f"   Символ: {symbol}")
        print(f"   Интервал: {interval}")
        print(f"   Период: {days} дней")
        print(f"   Примерное количество свечей: {days * 24 if interval == '1h' else days * 24 * 60}")
        
        # Загружаем данные порциями по 90 дней для стабильности
        chunk_size = 90
        all_chunks = []
        
        for chunk_start in range(0, days, chunk_size):
            chunk_days = min(chunk_size, days - chunk_start)
            
            print(f"\n📦 Загрузка части {chunk_start//chunk_size + 1}: дни {chunk_start}-{chunk_start + chunk_days}")
            
            # Загружаем кусок
            chunk_df = self.get_historical_data(symbol, interval, chunk_days)
            
            if not chunk_df.empty:
                all_chunks.append(chunk_df)
                print(f"   ✅ Загружено {len(chunk_df)} свечей для этой части")
            else:
                print(f"   ❌ Не удалось загрузить часть {chunk_start//chunk_size + 1}")
                break
            
            # Большая пауза между частями
            time.sleep(1.0)
        
        # Объединяем все части
        if all_chunks:
            print(f"\n🔄 Объединение {len(all_chunks)} частей...")
            final_df = pd.concat(all_chunks, ignore_index=True)
            final_df = final_df.sort_values('timestamp').drop_duplicates(subset=['timestamp']).reset_index(drop=True)
            
            # Сохраняем большой датасет в отдельный кэш
            large_cache_file = self.cache_dir / f"{symbol}_{interval}_LARGE_{days}days.pkl"
            with open(large_cache_file, 'wb') as f:
                pickle.dump(final_df, f)
            
            print(f"✅ БОЛЬШОЙ ДАТАСЕТ ГОТОВ!")
            print(f"   Общее количество свечей: {len(final_df)}")
            print(f"   Период: {final_df['timestamp'].min()} - {final_df['timestamp'].max()}")
            print(f"   Размер файла: {large_cache_file.stat().st_size / (1024*1024):.1f} MB")
            print(f"   Сохранено в: {large_cache_file}")
            
            return final_df
        
        return pd.DataFrame()
    
    def get_realtime_price(self, symbol: str = "BTCUSDT") -> Dict[str, float]:
        """
        Получение текущей цены в реальном времени
        
        Args:
            symbol: Торговая пара
            
        Returns:
            Словарь с текущей ценой и объемом
        """
        try:
            url = f"{self.binance_base_url}/ticker/24hr"
            params = {'symbol': symbol}
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'price': float(data['lastPrice']),
                'volume': float(data['volume']),
                'high_24h': float(data['highPrice']),
                'low_24h': float(data['lowPrice']),
                'change_24h': float(data['priceChangePercent'])
            }
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Ошибка получения текущей цены: {e}")
            return {}
    
    def get_coingecko_data(self, coin_id: str = "bitcoin", vs_currency: str = "usd",
                          days: int = 30) -> pd.DataFrame:
        """
        Альтернативный источник: CoinGecko API
        
        Args:
            coin_id: ID монеты в CoinGecko (bitcoin для BTC)
            vs_currency: Валюта котировки (usd)
            days: Количество дней
            
        Returns:
            DataFrame с данными
        """
        try:
            url = f"{self.coingecko_base_url}/coins/{coin_id}/ohlc"
            params = {
                'vs_currency': vs_currency,
                'days': days
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Преобразуем в DataFrame
            df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # CoinGecko не предоставляет объем в OHLC, добавляем заглушку
            df['volume'] = 0
            
            return df
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Ошибка загрузки с CoinGecko: {e}")
            return pd.DataFrame()
    
    def prepare_data_for_indicator(self, df: pd.DataFrame) -> Tuple[Dict[str, List[float]], List[float]]:
        """
        Подготовка данных для AI индикатора
        
        Args:
            df: DataFrame с OHLCV данными
            
        Returns:
            Кортеж (ohlc_dict, volumes_list)
        """
        if df.empty:
            return {}, []
        
        ohlc_data = {
            'open': df['open'].tolist(),
            'high': df['high'].tolist(),
            'low': df['low'].tolist(),
            'close': df['close'].tolist()
        }
        
        volumes = df['volume'].tolist()
        
        return ohlc_data, volumes
    
    def get_multiple_timeframes(self, symbol: str = "BTCUSDT", 
                               timeframes: List[str] = ['15m', '1h', '4h'],
                               days: int = 30) -> Dict[str, pd.DataFrame]:
        """
        Загрузка данных для нескольких таймфреймов
        
        Args:
            symbol: Торговая пара
            timeframes: Список таймфреймов
            days: Количество дней истории
            
        Returns:
            Словарь с данными для каждого таймфрейма
        """
        mtf_data = {}
        
        for tf in timeframes:
            print(f"⏰ Загрузка данных для таймфрейма {tf}...")
            df = self.get_historical_data(symbol, tf, days)
            if not df.empty:
                mtf_data[tf] = df
            time.sleep(0.5)  # Задержка между запросами
        
        return mtf_data
    
    def stream_realtime_updates(self, symbol: str = "BTCUSDT", 
                               callback=None, interval: int = 5):
        """
        Стриминг данных в реальном времени
        
        Args:
            symbol: Торговая пара
            callback: Функция обратного вызова для обработки новых данных
            interval: Интервал обновления в секундах
        """
        print(f"🔄 Запуск стриминга данных {symbol} (обновление каждые {interval} сек)...")
        
        try:
            while True:
                price_data = self.get_realtime_price(symbol)
                
                if price_data and callback:
                    callback(price_data)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️  Стриминг остановлен")
    
    def clear_cache(self):
        """Очистка кэша данных"""
        cache_files = list(self.cache_dir.glob("*.pkl"))
        for file in cache_files:
            file.unlink()
        print(f"🗑️  Очищено {len(cache_files)} файлов кэша")


# Вспомогательные функции
def test_data_loader():
    """Тестирование загрузчика данных"""
    loader = CryptoDataLoader()
    
    # Тест загрузки исторических данных
    print("📊 Тестирование загрузки данных BTCUSDT...")
    df = loader.get_historical_data("BTCUSDT", "1h", days=7)
    
    if not df.empty:
        print(f"✅ Загружено {len(df)} свечей")
        print(f"   Период: {df['timestamp'].min()} - {df['timestamp'].max()}")
        print(f"   Последняя цена: ${df.iloc[-1]['close']:,.2f}")
    
    # Тест текущей цены
    print("\n💰 Текущая цена BTCUSDT:")
    price_data = loader.get_realtime_price("BTCUSDT")
    if price_data:
        print(f"   Цена: ${price_data['price']:,.2f}")
        print(f"   Изменение 24ч: {price_data['change_24h']:.2f}%")
    
    return loader, df


if __name__ == "__main__":
    test_data_loader()