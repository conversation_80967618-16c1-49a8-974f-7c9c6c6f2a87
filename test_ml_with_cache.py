#!/usr/bin/env python3
"""
Тест ML модели с данными из кеша
"""

import sys
import os
import pickle
import numpy as np
import pandas as pd
from ai_indicator_full import AITrendReversalIndicator

def load_cached_data():
    """Загрузка данных из кеша"""
    cache_files = [
        "training_data_cache/BTCUSDT_1h_LARGE_365days.pkl",
        "training_data_cache/BTCUSDT_1h_90days.pkl",
        "training_data_cache/BTCUSDT_4h_LARGE_730days.pkl"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            print(f"📂 Загружаем данные из: {cache_file}")
            try:
                with open(cache_file, 'rb') as f:
                    df = pickle.load(f)
                print(f"✅ Загружено {len(df)} свечей")
                print(f"📅 Период: {df['timestamp'].min()} - {df['timestamp'].max()}")
                return df
            except Exception as e:
                print(f"❌ Ошибка загрузки {cache_file}: {e}")
                continue
    
    print("❌ Не найдено подходящих файлов кеша")
    return None

def prepare_data_for_indicator(df):
    """Подготовка данных для индикатора"""
    ohlc_data = {
        'open': df['open'].tolist(),
        'high': df['high'].tolist(),
        'low': df['low'].tolist(),
        'close': df['close'].tolist()
    }
    
    volumes = df['volume'].tolist()
    
    return ohlc_data, volumes

def test_ml_with_real_data():
    """Тест ML модели с реальными данными"""
    print("🧪 Тест ML модели с реальными данными из кеша...")
    
    # Загружаем данные из кеша
    df = load_cached_data()
    if df is None:
        return False
    
    # Подготавливаем данные
    ohlc_data, volumes = prepare_data_for_indicator(df)
    
    # Создание индикатора
    config = {
        'enable_ai_learning': True,
        'models_dir': 'test_models_cache',
        'auto_save_enabled': False,
        'auto_save_interval': 50,
    }
    
    indicator = AITrendReversalIndicator(config)
    
    print("\n🤖 Обработка реальных данных и обучение модели...")
    
    valid_signals = []
    all_signals = []
    
    # Минимальное окно для анализа
    min_window = 200
    
    # Ограничиваем количество данных для теста (берем последние 500 свечей)
    max_bars = min(len(ohlc_data['close']), min_window + 300)
    
    for i in range(min_window, max_bars):
        if i % 50 == 0:
            progress = (i - min_window) / (max_bars - min_window) * 100
            print(f"   Прогресс: {progress:.1f}% ({i}/{max_bars})")
        
        # Подготовка данных для текущего окна
        window_ohlc = {
            'open': ohlc_data['open'][:i+1],
            'high': ohlc_data['high'][:i+1],
            'low': ohlc_data['low'][:i+1],
            'close': ohlc_data['close'][:i+1]
        }
        window_volumes = volumes[:i+1]
        
        # Анализ текущего бара
        signal = indicator.process_bar(window_ohlc, window_volumes, i)
        all_signals.append(signal)
        
        # Сохраняем валидные сигналы
        if signal.is_valid:
            valid_signals.append(signal)
            timestamp = df.iloc[i]['timestamp']
            price = df.iloc[i]['close']
            
            print(f"\n✅ Валидный сигнал на {timestamp}:")
            print(f"   Тип: {signal.signal_type}")
            print(f"   Цена: ${price:,.2f}")
            print(f"   Вероятность: {signal.probability:.1f}%")
            print(f"   Уверенность: {signal.confidence:.1f}%")
            print(f"   Обоснование: {signal.reasoning}")
    
    # Статистика обучения
    print(f"\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ С РЕАЛЬНЫМИ ДАННЫМИ:")
    print(f"   Обработано баров: {len(all_signals)}")
    print(f"   Всего сигналов: {len([s for s in all_signals if s.signal_type != 'none'])}")
    print(f"   Валидных сигналов: {len(valid_signals)}")
    
    if valid_signals:
        bullish = len([s for s in valid_signals if s.signal_type == 'bullish_reversal'])
        bearish = len([s for s in valid_signals if s.signal_type == 'bearish_reversal'])
        
        print(f"   Бычьих разворотов: {bullish}")
        print(f"   Медвежьих разворотов: {bearish}")
        
        avg_prob = np.mean([s.probability for s in valid_signals])
        avg_conf = np.mean([s.confidence for s in valid_signals])
        
        print(f"\n📈 СРЕДНИЕ ПОКАЗАТЕЛИ:")
        print(f"   Вероятность: {avg_prob:.1f}%")
        print(f"   Уверенность: {avg_conf:.1f}%")
    
    # Статистика модели
    stats = indicator.get_performance_stats()
    print(f"\n🤖 СТАТИСТИКА МОДЕЛИ:")
    print(f"   Байесовская точность: {stats['model_accuracy']:.1f}%")
    print(f"   Обучающих образцов: {stats['training_samples']}")
    
    # Проверяем ML модель
    ml_success = False
    if indicator.ensemble_model.is_trained:
        print(f"   ML модель обучена: ✅")
        if hasattr(indicator.ensemble_model, 'cross_val_scores') and indicator.ensemble_model.cross_val_scores:
            cv_score = np.mean(indicator.ensemble_model.cross_val_scores)
            print(f"   ML точность (CV): {cv_score:.3f}")
            
            # Проверяем, что точность разумная
            if not np.isnan(cv_score) and cv_score > 0.1:
                ml_success = True
                print("   ✅ ML модель работает корректно")
            else:
                print(f"   ❌ ML модель дает подозрительную точность: {cv_score}")
        
        # Тестируем предсказание
        if len(indicator.ml_features_history) > 0:
            test_features = indicator.ml_features_history[-1]
            try:
                prediction = indicator.predict_ensemble(np.array([test_features]))
                confidence = indicator.get_ensemble_confidence(test_features)
                print(f"   Тестовое предсказание: {prediction[0]}")
                print(f"   Уверенность ансамбля: {confidence:.3f}")
                
                # Проверяем валидность предсказания
                if prediction[0] in [-1, 0, 1] and not np.isnan(confidence):
                    print("   ✅ Предсказание работает корректно")
                    ml_success = True
                else:
                    print(f"   ❌ Некорректное предсказание: {prediction[0]}, confidence: {confidence}")
                    
            except Exception as e:
                print(f"   ❌ Ошибка предсказания: {e}")
                import traceback
                traceback.print_exc()
                ml_success = False
    else:
        print(f"   ML модель обучена: ❌")
        if len(indicator.ml_features_history) < 50:
            print(f"   Причина: Недостаточно данных ({len(indicator.ml_features_history)}/50)")
        else:
            print(f"   Причина: Ошибка обучения")
    
    # Проверяем качество сигналов
    signal_quality = len(valid_signals) > 0 and len(valid_signals) < len(all_signals) * 0.5  # Не более 50% валидных сигналов
    
    print(f"\n📊 ОЦЕНКА КАЧЕСТВА:")
    print(f"   ML модель работает: {'✅' if ml_success else '❌'}")
    print(f"   Качество сигналов: {'✅' if signal_quality else '❌'}")
    print(f"   Общий результат: {'✅ УСПЕХ' if ml_success and signal_quality else '❌ ТРЕБУЮТСЯ ДОРАБОТКИ'}")
    
    return ml_success and signal_quality

if __name__ == "__main__":
    print("🚀 Запуск теста ML модели с реальными данными\n")
    
    # Создаем директорию для тестовых моделей
    os.makedirs('test_models_cache', exist_ok=True)
    
    try:
        success = test_ml_with_real_data()
        
        if success:
            print("\n🎉 Тест с реальными данными прошел успешно!")
            print("✅ Все исправления ML модели работают корректно")
        else:
            print("\n⚠️  Тест показал проблемы, но это может быть нормально")
            print("   Проверьте логи выше для деталей")
            
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Очистка тестовых файлов
        import shutil
        if os.path.exists('test_models_cache'):
            shutil.rmtree('test_models_cache')
            print("\n🧹 Тестовые файлы очищены")
