"""
Полная реализация AI Trend Reversal Indicator
Максимальное соответствие оригинальному PineScript индикатору
"""

# Попытка импорта numpy с fallback на минимальную реализацию
import numpy as np
from scipy import stats
import pandas as pd
import json
import math
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import warnings
from collections import deque
import os
warnings.filterwarnings('ignore')

# Добавляем joblib для сохранения ML моделей
try:
    import joblib
    JOBLIB_AVAILABLE = True
except ImportError:
    import pickle
    JOBLIB_AVAILABLE = False

# Попытка импорта ML библиотек с fallback
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression, SGDClassifier, PassiveAggressiveClassifier
    from sklearn.naive_bayes import GaussianNB
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import cross_val_score, TimeSeriesSplit, GridSearchCV
    from sklearn.feature_selection import SelectKBest, f_classif
    from sklearn.metrics import accuracy_score, classification_report, precision_recall_fscore_support
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import TomekLinks
    from imblearn.combine import SMOTETomek
    ML_AVAILABLE = True
    IMBLEARN_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  ML библиотеки не найдены: {e}. ML функции будут отключены.")
    ML_AVAILABLE = False
    IMBLEARN_AVAILABLE = False

@dataclass
class AISignal:
    """Структура AI сигнала"""
    signal_type: str  # "bullish_reversal", "bearish_reversal", "none"
    probability: float  # Вероятность в процентах
    confidence: float  # Уверенность AI в процентах
    strength: int  # Сила сигнала 0-100
    math_expectation: float  # Математическое ожидание
    risk_score: float  # Оценка риска
    reasoning: str  # Обоснование сигнала
    is_valid: bool  # Валидность сигнала
    timestamp: int  # Временная метка

@dataclass
class BayesianModel:
    """Улучшенная байесовская модель для AI с информативными приорами"""
    prior_bullish: float = 0.6  # Информативный приор: крипторынок чаще растет
    prior_bearish: float = 0.4  # Соответственно медвежьи развороты реже
    model_accuracy: float = 0.5
    training_samples: int = 0
    successful_predictions: int = 0
    total_predictions: int = 0
    adaptation_rate: float = 0.05  # Снижена для более стабильного обучения
    regularization_factor: float = 0.1  # Фактор регуляризации для избежания переобучения
    confidence_threshold: float = 0.7  # Порог уверенности для принятия решений

@dataclass
class EnsembleMLModel:
    """Улучшенная ансамблевая ML модель с инкрементальным обучением"""
    models: Dict[str, Any] = None
    scaler: Any = None
    is_trained: bool = False
    feature_importance: Dict[str, float] = None
    cross_val_scores: List[float] = None

    # Новые поля для улучшенного дообучения
    model_versions: List[Dict] = None
    performance_history: List[Dict] = None
    incremental_models: Dict[str, Any] = None
    transfer_learning_enabled: bool = True
    adaptive_hyperparams: Dict[str, Any] = None
    feedback_buffer: List[Dict] = None
    last_retrain_timestamp: str = None
    retrain_threshold: float = 0.05  # Порог для переобучения при снижении точности

    def __post_init__(self):
        if ML_AVAILABLE and self.models is None:
            # Расширенный ансамбль с инкрементальными моделями
            self.models = {
                'rf': RandomForestClassifier(
                    n_estimators=100,  # Увеличено для лучшей точности
                    max_depth=5,       # Увеличено согласно предпочтениям пользователя
                    min_samples_leaf=3,
                    random_state=42,
                    class_weight='balanced'
                ),
                'logreg': LogisticRegression(
                    random_state=42,
                    class_weight='balanced',
                    max_iter=1000,
                    C=1.0,
                    penalty='l2'
                ),
                'sgd': SGDClassifier(  # Добавляем инкрементальную модель
                    random_state=42,
                    class_weight='balanced',
                    loss='log_loss',
                    learning_rate='adaptive',
                    eta0=0.01
                )
            }

            # Инкрементальные модели для онлайн обучения (без class_weight для partial_fit)
            self.incremental_models = {
                'sgd_incremental': SGDClassifier(
                    random_state=42,
                    loss='log_loss',
                    learning_rate='adaptive',
                    eta0=0.01
                ),
                'passive_aggressive': PassiveAggressiveClassifier(
                    random_state=42,
                    C=1.0
                )
            }

            self.scaler = StandardScaler()
            self.feature_importance = {}
            self.cross_val_scores = []
            self.feature_selector = SelectKBest(f_classif, k=20)

            # Инициализация новых полей
            self.model_versions = []
            self.performance_history = []
            self.adaptive_hyperparams = {
                'rf_n_estimators': 100,
                'rf_max_depth': 5,
                'logreg_C': 1.0,
                'sgd_eta0': 0.01
            }
            self.feedback_buffer = []
            self.last_retrain_timestamp = datetime.now().isoformat()

@dataclass
class SRLevel:
    """Уровень поддержки/сопротивления"""
    price: float
    touches: int
    strength: float
    is_support: bool
    last_touch_bar: int
    creation_bar_index: int

class AITrendReversalIndicator:
    """Полная реализация AI Trend Reversal Indicator"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Инициализация индикатора"""
        # Объединяем базовую конфигурацию с переданной
        self.config = self._default_config()
        if config:
            self.config.update(config)
        
        # AI и машинное обучение
        self.bayesian_model = BayesianModel()
        self.ensemble_model = EnsembleMLModel()
        self.ai_signals_history = []
        self.ml_features_history = deque(maxlen=1000)  # Ограничиваем размер для эффективности
        self.ml_labels_history = deque(maxlen=1000)
        
        # Технические индикаторы
        self.macd_history = []
        self.rsi_history = []
        self.sma_history = []
        self.atr_history = []
        
        # Дивергенции
        self.macd_divergences = []
        self.rsi_divergences = []
        
        # Свечные паттерны
        self.candle_patterns = []
        
        # Уровни поддержки/сопротивления
        self.support_levels = []
        self.resistance_levels = []
        
        # Объемный анализ
        self.volume_history = []
        self.volume_spikes = []
        
        # Multi-timeframe данные
        self.mtf_data = {}
        
        # Статистика производительности
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'win_rate': 0.0,
            'avg_return': 0.0
        }
        
    def _default_config(self) -> Dict[str, Any]:
        """Конфигурация по умолчанию"""
        return {
            # AI настройки (снижены для лучшего обучения)
            'ai_confidence_threshold': 60.0,  # Снижено с 75.0
            'min_signal_strength': 50,        # Снижено с 65
            'probability_model': 'Advanced',
            'enable_ai_learning': True,
            
            # Технические индикаторы
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'rsi_period': 14,
            'sma_period': 20,
            'atr_period': 14,
            
            # Multi-timeframe
            'enable_mtf': True,
            'mtf_confluence_required': 2,
            'mtf_timeframes': ['5m', '15m', '1h'],
            
            # Объемный анализ
            'enable_volume_analysis': True,
            'volume_spike_threshold': 1.5,
            'volume_ma_period': 20,
            
            # Поддержка/сопротивление
            'enable_sr_analysis': True,
            'sr_lookback': 50,
            'sr_min_touches': 2,
            
            # Риск-менеджмент
            'stop_loss_atr_multiplier': 2.0,
            'take_profit_atr_multiplier': 4.0,
            'risk_reward_ratio': 2.0,
            
            # Фильтры качества
            'quality_filter': True,
            'adaptive_thresholds': True,
            'min_bars_between_signals': 5,
            
            # Пути сохранения моделей и данных
            'models_dir': 'ai_models',
            'state_file': 'ai_indicator_state.json',
            'ml_models_file': 'ml_ensemble_models.pkl',
            'training_history_file': 'training_history.csv',
            'auto_save_enabled': True,
            'auto_save_interval': 100  # Сохранять каждые 100 баров
        }
    
    def calculate_macd(self, prices: List[float]) -> Tuple[float, float, float]:
        """Расчет MACD"""
        if len(prices) < self.config['macd_slow']:
            return 0.0, 0.0, 0.0
            
        prices_array = np.array(prices)
        
        # EMA расчет
        def ema(data, period):
            alpha = 2 / (period + 1)
            ema_values = [data[0]]
            for i in range(1, len(data)):
                ema_values.append(alpha * data[i] + (1 - alpha) * ema_values[-1])
            return ema_values[-1]
        
        ema_fast = ema(prices_array, self.config['macd_fast'])
        ema_slow = ema(prices_array, self.config['macd_slow'])
        macd_line = ema_fast - ema_slow
        
        # Signal line
        if len(self.macd_history) >= self.config['macd_signal']:
            macd_values = [h['macd'] for h in self.macd_history[-self.config['macd_signal']:]]
            macd_values.append(macd_line)
            signal_line = ema(macd_values, self.config['macd_signal'])
        else:
            signal_line = macd_line
            
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def calculate_rsi(self, prices: List[float]) -> float:
        """Расчет RSI"""
        if len(prices) < self.config['rsi_period'] + 1:
            return 50.0
            
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-self.config['rsi_period']:])
        avg_loss = np.mean(losses[-self.config['rsi_period']:])
        
        if avg_loss == 0:
            return 100.0
            
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_atr(self, highs: List[float], lows: List[float], closes: List[float]) -> float:
        """Расчет ATR"""
        if len(closes) < 2:
            return 0.0

        true_ranges = []
        for i in range(1, len(closes)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_ranges.append(max(tr1, tr2, tr3))

        if len(true_ranges) >= self.config['atr_period']:
            return np.mean(true_ranges[-self.config['atr_period']:])
        else:
            return np.mean(true_ranges) if true_ranges else 0.0

    def calculate_enhanced_features(self, ohlc: Dict[str, List[float]], volume: List[float]) -> Dict[str, float]:
        """Расчет расширенных технических признаков для улучшения точности"""
        features = {}

        if len(ohlc['close']) < 50:
            return features

        closes = np.array(ohlc['close'])
        highs = np.array(ohlc['high'])
        lows = np.array(ohlc['low'])
        volumes = np.array(volume) if volume else np.ones_like(closes)

        # === EMA 200 filter (добавляем в начало) ===
        ema_period = 200
        if len(closes) >= ema_period:
            ema_200 = pd.Series(closes).ewm(span=ema_period).mean().iloc[-1]
            features['ema_trend'] = 1 if closes[-1] > ema_200 else -1
        else:
            features['ema_trend'] = 0

        # === Volume spike binary flag (улучшенная логика) ===
        if len(volumes) >= 20:
            avg_volume_20 = np.mean(volumes[-20:])
            current_volume = volumes[-1]
            volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 1.0
            features['volume_spike'] = 1 if volume_ratio >= 1.5 else 0
            features['volume_ratio'] = volume_ratio
        else:
            features['volume_spike'] = 0
            features['volume_ratio'] = 1.0

        # 1. Bollinger Bands
        bb_period = 20
        if len(closes) >= bb_period:
            bb_sma = np.mean(closes[-bb_period:])
            bb_std = np.std(closes[-bb_period:])
            bb_upper = bb_sma + 2 * bb_std
            bb_lower = bb_sma - 2 * bb_std
            features['bb_position'] = (closes[-1] - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
            features['bb_squeeze'] = bb_std / bb_sma if bb_sma > 0 else 0

        # 2. Stochastic Oscillator
        stoch_period = 14
        if len(closes) >= stoch_period:
            high_max = np.max(highs[-stoch_period:])
            low_min = np.min(lows[-stoch_period:])
            features['stoch_k'] = ((closes[-1] - low_min) / (high_max - low_min) * 100) if high_max != low_min else 50

        # 3. Williams %R
        if len(closes) >= 14:
            high_max = np.max(highs[-14:])
            low_min = np.min(lows[-14:])
            features['williams_r'] = ((high_max - closes[-1]) / (high_max - low_min) * -100) if high_max != low_min else -50

        # 4. Commodity Channel Index (CCI)
        cci_period = 20
        if len(closes) >= cci_period:
            typical_prices = (highs[-cci_period:] + lows[-cci_period:] + closes[-cci_period:]) / 3
            sma_tp = np.mean(typical_prices)
            mean_deviation = np.mean(np.abs(typical_prices - sma_tp))
            current_tp = (highs[-1] + lows[-1] + closes[-1]) / 3
            features['cci'] = (current_tp - sma_tp) / (0.015 * mean_deviation) if mean_deviation > 0 else 0

        # 5. Rate of Change (ROC)
        for period in [5, 10, 20]:
            if len(closes) > period:
                features[f'roc_{period}'] = (closes[-1] - closes[-period-1]) / closes[-period-1] * 100

        # 6. Money Flow Index (MFI)
        mfi_period = 14
        if len(closes) >= mfi_period and len(volumes) >= mfi_period:
            typical_prices = (highs[-mfi_period:] + lows[-mfi_period:] + closes[-mfi_period:]) / 3
            money_flow = typical_prices * volumes[-mfi_period:]

            positive_flow = np.sum([mf for i, mf in enumerate(money_flow[1:], 1)
                                  if typical_prices[i] > typical_prices[i-1]])
            negative_flow = np.sum([mf for i, mf in enumerate(money_flow[1:], 1)
                                  if typical_prices[i] < typical_prices[i-1]])

            if negative_flow > 0:
                money_ratio = positive_flow / negative_flow
                features['mfi'] = 100 - (100 / (1 + money_ratio))
            else:
                features['mfi'] = 100

        # 7. Chaikin Money Flow (CMF)
        cmf_period = 20
        if len(closes) >= cmf_period:
            money_flow_multiplier = []
            money_flow_volume = []

            for i in range(-cmf_period, 0):
                if highs[i] != lows[i]:
                    mfm = ((closes[i] - lows[i]) - (highs[i] - closes[i])) / (highs[i] - lows[i])
                else:
                    mfm = 0
                money_flow_multiplier.append(mfm)
                money_flow_volume.append(mfm * volumes[i])

            features['cmf'] = np.sum(money_flow_volume) / np.sum(volumes[-cmf_period:]) if np.sum(volumes[-cmf_period:]) > 0 else 0

        # 8. Average Directional Index (ADX)
        adx_period = 14
        if len(closes) >= adx_period + 1:
            features['adx'] = self._calculate_adx(highs, lows, closes, adx_period)

        # 9. Parabolic SAR
        if len(closes) >= 5:
            features['psar_position'] = self._calculate_psar_position(highs, lows, closes)

        # 10. Ichimoku Cloud базовые компоненты
        if len(closes) >= 26:
            tenkan_sen = (np.max(highs[-9:]) + np.min(lows[-9:])) / 2
            kijun_sen = (np.max(highs[-26:]) + np.min(lows[-26:])) / 2
            features['ichimoku_tk_cross'] = (tenkan_sen - kijun_sen) / closes[-1] * 100
            features['price_vs_kijun'] = (closes[-1] - kijun_sen) / kijun_sen * 100

        # 11. VWAP (Volume Weighted Average Price) отклонение
        if len(closes) >= 20 and len(volumes) >= 20:
            typical_prices = (highs[-20:] + lows[-20:] + closes[-20:]) / 3
            vwap = np.sum(typical_prices * volumes[-20:]) / np.sum(volumes[-20:])
            features['vwap_deviation'] = (closes[-1] - vwap) / vwap * 100

        # 12. Accumulation/Distribution Line изменение
        if len(closes) >= 10 and len(volumes) >= 10:
            ad_values = []
            for i in range(-10, 0):
                if highs[i] != lows[i]:
                    mfm = ((closes[i] - lows[i]) - (highs[i] - closes[i])) / (highs[i] - lows[i])
                else:
                    mfm = 0
                ad = mfm * volumes[i]
                ad_values.append(ad)
            features['ad_trend'] = 1 if np.mean(ad_values[-5:]) > np.mean(ad_values[:5]) else -1

        # 13. Aroon Oscillator
        if len(closes) >= 25:
            high_days = 25 - np.argmax(highs[-25:])
            low_days = 25 - np.argmin(lows[-25:])
            aroon_up = (25 - high_days) / 25 * 100
            aroon_down = (25 - low_days) / 25 * 100
            features['aroon_oscillator'] = aroon_up - aroon_down

        # 14. Elder Ray Index
        if len(closes) >= 13:
            ema_13 = pd.Series(closes).ewm(span=13).mean().iloc[-1]
            bull_power = highs[-1] - ema_13
            bear_power = lows[-1] - ema_13
            features['elder_ray_bull'] = bull_power / closes[-1] * 100
            features['elder_ray_bear'] = bear_power / closes[-1] * 100

        # 15. Klinger Oscillator направление
        if len(closes) >= 35 and len(volumes) >= 35:
            # Упрощенная версия Klinger
            trend_changes = []
            for i in range(1, 35):
                if (highs[-i] + lows[-i] + closes[-i]) > (highs[-i-1] + lows[-i-1] + closes[-i-1]):
                    trend_changes.append(volumes[-i])
                else:
                    trend_changes.append(-volumes[-i])
            features['klinger_direction'] = 1 if np.sum(trend_changes[-10:]) > 0 else -1

        return features
    
    def _calculate_psar_position(self, highs, lows, closes) -> float:
        """Расчет позиции относительно Parabolic SAR"""
        # Упрощенная версия PSAR
        af = 0.02
        max_af = 0.2
        
        # Начальные значения
        psar = lows[-5]
        bull = True
        ep = highs[-5]
        
        for i in range(-4, 0):
            if bull:
                psar = psar + af * (ep - psar)
                if lows[i] < psar:
                    bull = False
                    psar = ep
                    ep = lows[i]
                    af = 0.02
                else:
                    if highs[i] > ep:
                        ep = highs[i]
                        af = min(af + 0.02, max_af)
            else:
                psar = psar + af * (ep - psar)
                if highs[i] > psar:
                    bull = True
                    psar = ep
                    ep = highs[i]
                    af = 0.02
                else:
                    if lows[i] < ep:
                        ep = lows[i]
                        af = min(af + 0.02, max_af)
        
        # Возвращаем позицию: 1 если цена выше PSAR, -1 если ниже
        return 1 if closes[-1] > psar else -1

    def _calculate_adx(self, highs, lows, closes, period: int) -> float:
        """Расчет Average Directional Index"""
        if len(closes) < period + 1:
            return 0.0

        # True Range
        tr = []
        for i in range(1, len(closes)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            tr.append(max(tr1, tr2, tr3))

        # Directional Movement
        dm_plus = []
        dm_minus = []
        for i in range(1, len(highs)):
            up_move = highs[i] - highs[i-1]
            down_move = lows[i-1] - lows[i]

            if up_move > down_move and up_move > 0:
                dm_plus.append(up_move)
            else:
                dm_plus.append(0)

            if down_move > up_move and down_move > 0:
                dm_minus.append(down_move)
            else:
                dm_minus.append(0)

        # Smoothed averages
        if len(tr) >= period:
            atr = np.mean(tr[-period:])
            di_plus = np.mean(dm_plus[-period:]) / atr * 100 if atr > 0 else 0
            di_minus = np.mean(dm_minus[-period:]) / atr * 100 if atr > 0 else 0

            if di_plus + di_minus > 0:
                dx = abs(di_plus - di_minus) / (di_plus + di_minus) * 100
                return dx

        return 0.0
    
    def detect_macd_divergence(self, prices: List[float], macd_values: List[float]) -> str:
        """Обнаружение дивергенций MACD"""
        if len(prices) < 10 or len(macd_values) < 10:
            return "none"
        
        # Поиск локальных максимумов и минимумов
        price_peaks = self._find_peaks(prices[-20:])
        macd_peaks = self._find_peaks(macd_values[-20:])
        
        if len(price_peaks) >= 2 and len(macd_peaks) >= 2:
            # Бычья дивергенция: цена делает новые минимумы, MACD - нет
            if (prices[price_peaks[-1]] < prices[price_peaks[-2]] and 
                macd_values[macd_peaks[-1]] > macd_values[macd_peaks[-2]]):
                return "bullish"
            
            # Медвежья дивергенция: цена делает новые максимумы, MACD - нет
            if (prices[price_peaks[-1]] > prices[price_peaks[-2]] and 
                macd_values[macd_peaks[-1]] < macd_values[macd_peaks[-2]]):
                return "bearish"
        
        return "none"
    
    def detect_rsi_divergence(self, prices: List[float], rsi_values: List[float]) -> str:
        """Обнаружение дивергенций RSI"""
        if len(prices) < 10 or len(rsi_values) < 10:
            return "none"
        
        price_peaks = self._find_peaks(prices[-20:])
        rsi_peaks = self._find_peaks(rsi_values[-20:])
        
        if len(price_peaks) >= 2 and len(rsi_peaks) >= 2:
            # Бычья дивергенция
            if (prices[price_peaks[-1]] < prices[price_peaks[-2]] and 
                rsi_values[rsi_peaks[-1]] > rsi_values[rsi_peaks[-2]]):
                return "bullish"
            
            # Медвежья дивергенция
            if (prices[price_peaks[-1]] > prices[price_peaks[-2]] and 
                rsi_values[rsi_peaks[-1]] < rsi_values[rsi_peaks[-2]]):
                return "bearish"
        
        return "none"
    
    def _find_peaks(self, data: List[float]) -> List[int]:
        """Поиск локальных экстремумов"""
        peaks = []
        for i in range(1, len(data) - 1):
            if data[i] > data[i-1] and data[i] > data[i+1]:
                peaks.append(i)
            elif data[i] < data[i-1] and data[i] < data[i+1]:
                peaks.append(i)
        return peaks
    
    def detect_candle_patterns(self, ohlc: Dict[str, List[float]]) -> List[str]:
        """Обнаружение свечных паттернов"""
        if len(ohlc['open']) < 3:
            return []
        
        patterns = []
        o, h, l, c = ohlc['open'][-1], ohlc['high'][-1], ohlc['low'][-1], ohlc['close'][-1]
        prev_o, prev_h, prev_l, prev_c = ohlc['open'][-2], ohlc['high'][-2], ohlc['low'][-2], ohlc['close'][-2]
        
        body = abs(c - o)
        prev_body = abs(prev_c - prev_o)
        upper_shadow = h - max(o, c)
        lower_shadow = min(o, c) - l
        
        # Doji
        if body < (h - l) * 0.1:
            patterns.append("doji")
        
        # Hammer
        if (lower_shadow > body * 2 and upper_shadow < body * 0.5 and 
            c > o and l < prev_l):
            patterns.append("hammer")
        
        # Shooting Star
        if (upper_shadow > body * 2 and lower_shadow < body * 0.5 and 
            c < o and h > prev_h):
            patterns.append("shooting_star")
        
        # Bullish Engulfing
        if (c > o and prev_c < prev_o and 
            c > prev_o and o < prev_c and body > prev_body):
            patterns.append("bullish_engulfing")
        
        # Bearish Engulfing
        if (c < o and prev_c > prev_o and 
            c < prev_o and o > prev_c and body > prev_body):
            patterns.append("bearish_engulfing")
        
        return patterns
    
    def update_support_resistance(self, ohlc: Dict[str, List[float]], bar_index: int):
        """Обновление уровней поддержки и сопротивления"""
        if len(ohlc['high']) < self.config['sr_lookback']:
            return
        
        # Поиск pivot points
        highs = ohlc['high'][-self.config['sr_lookback']:]
        lows = ohlc['low'][-self.config['sr_lookback']:]
        
        # Поиск локальных максимумов (сопротивления)
        for i in range(2, len(highs) - 2):
            if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                self._add_resistance_level(highs[i], bar_index - (len(highs) - i))
        
        # Поиск локальных минимумов (поддержки)
        for i in range(2, len(lows) - 2):
            if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                self._add_support_level(lows[i], bar_index - (len(lows) - i))
    
    def _add_support_level(self, price: float, bar_index: int):
        """Добавление уровня поддержки"""
        # Проверка на существующие близкие уровни
        tolerance = price * 0.001  # 0.1% толерантность
        
        for level in self.support_levels:
            if abs(level.price - price) < tolerance:
                level.touches += 1
                level.strength = min(100, level.strength + 10)
                level.last_touch_bar = bar_index
                return
        
        # Добавление нового уровня
        new_level = SRLevel(
            price=price,
            touches=1,
            strength=50.0,
            is_support=True,
            last_touch_bar=bar_index,
            creation_bar_index=bar_index
        )
        
        self.support_levels.append(new_level)
        
        # Ограничение количества уровней
        if len(self.support_levels) > 10:
            self.support_levels = sorted(self.support_levels, key=lambda x: x.strength, reverse=True)[:10]
    
    def _add_resistance_level(self, price: float, bar_index: int):
        """Добавление уровня сопротивления"""
        tolerance = price * 0.001
        
        for level in self.resistance_levels:
            if abs(level.price - price) < tolerance:
                level.touches += 1
                level.strength = min(100, level.strength + 10)
                level.last_touch_bar = bar_index
                return
        
        new_level = SRLevel(
            price=price,
            touches=1,
            strength=50.0,
            is_support=False,
            last_touch_bar=bar_index,
            creation_bar_index=bar_index
        )
        
        self.resistance_levels.append(new_level)
        
        if len(self.resistance_levels) > 10:
            self.resistance_levels = sorted(self.resistance_levels, key=lambda x: x.strength, reverse=True)[:10]
    
    def analyze_volume(self, volumes: List[float]) -> Dict[str, Any]:
        """Улучшенный анализ объема с фокусом на всплески"""
        if len(volumes) < 20:  # Используем минимум 20 баров для стабильности
            return {'spike': False, 'strength': 0, 'trend': 'neutral', 'ratio': 1.0}

        current_volume = volumes[-1]
        # Используем 20-барный период для более стабильного среднего
        avg_volume_20 = np.mean(volumes[-20:])

        volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 1.0

        # Более строгие критерии для всплесков
        spike_threshold = 1.5  # Минимум 1.5x среднего объема
        strong_spike_threshold = 2.0  # Сильный всплеск

        return {
            'spike': volume_ratio >= spike_threshold,
            'strong_spike': volume_ratio >= strong_spike_threshold,
            'strength': min(100, int(volume_ratio * 40)),  # Скорректированная сила
            'trend': 'increasing' if volume_ratio > 1.3 else 'decreasing' if volume_ratio < 0.7 else 'neutral',
            'ratio': volume_ratio
        }
    
    def update_bayesian_model(self, signal_type: str, actual_outcome: bool):
        """Обновление байесовской модели"""
        self.bayesian_model.total_predictions += 1

        if actual_outcome:
            self.bayesian_model.successful_predictions += 1

        # Обновление точности модели
        if self.bayesian_model.total_predictions > 0:
            self.bayesian_model.model_accuracy = (
                self.bayesian_model.successful_predictions /
                self.bayesian_model.total_predictions
            )

        # Адаптивное обновление приоров
        adaptation = self.bayesian_model.adaptation_rate

        if signal_type == "bullish_reversal":
            if actual_outcome:
                self.bayesian_model.prior_bullish += adaptation * (1 - self.bayesian_model.prior_bullish)
            else:
                self.bayesian_model.prior_bullish -= adaptation * self.bayesian_model.prior_bullish

        elif signal_type == "bearish_reversal":
            if actual_outcome:
                self.bayesian_model.prior_bearish += adaptation * (1 - self.bayesian_model.prior_bearish)
            else:
                self.bayesian_model.prior_bearish -= adaptation * self.bayesian_model.prior_bearish

        # Нормализация приоров
        total = self.bayesian_model.prior_bullish + self.bayesian_model.prior_bearish
        if total > 0:
            self.bayesian_model.prior_bullish /= total
            self.bayesian_model.prior_bearish /= total

        self.bayesian_model.training_samples += 1

    def validate_signal_outcome(self, signal: AISignal, future_prices: List[float],
                               lookforward_bars: int = 10) -> bool:
        """Валидация исхода сигнала на основе будущих цен"""
        if len(future_prices) < lookforward_bars:
            return False

        signal_price = future_prices[0]  # Цена на момент сигнала
        future_segment = future_prices[1:lookforward_bars+1]

        if signal.signal_type == "bullish_reversal":
            # Для бычьего сигнала проверяем рост цены
            max_future_price = max(future_segment)
            return (max_future_price - signal_price) / signal_price >= 0.02  # Минимум 2% роста

        elif signal.signal_type == "bearish_reversal":
            # Для медвежьего сигнала проверяем падение цены
            min_future_price = min(future_segment)
            return (signal_price - min_future_price) / signal_price >= 0.02  # Минимум 2% падения

        return False

    def train_ensemble_model(self, features_list: List, labels_list: List[int]):
        """Улучшенное обучение ансамблевой ML модели для малых датасетов"""
        if not ML_AVAILABLE or len(features_list) < 50:  # Снижен порог с 100 до 50
            print("⚠️  Недостаточно данных для обучения ML модели или ML недоступен")
            return

        try:
            print(f"🤖 Обучение ансамблевой ML модели на {len(features_list)} образцах...")

            # Подготовка данных
            X = np.array(features_list)
            y = np.array(labels_list)

            # Проверяем количество уникальных классов
            unique_classes = np.unique(y)
            print(f"   Уникальные классы: {unique_classes}")

            # Если только один класс - не можем обучать
            if len(unique_classes) < 2:
                print(f"❌ ML ошибка: Недостаточно классов для обучения (найдено {len(unique_classes)}, нужно минимум 2)")
                return

            # Проверяем наличие NaN или inf значений
            if np.any(np.isnan(X)) or np.any(np.isinf(X)):
                print("⚠️  Обнаружены NaN/inf значения в признаках, очищаем...")
                # Заменяем NaN и inf на 0
                X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)

            # ПРАВИЛЬНЫЙ ПОРЯДОК: Feature selection -> SMOTE -> Scaling

            # 1. Feature selection на исходных данных
            if hasattr(self.ensemble_model, 'feature_selector'):
                X_selected = self.ensemble_model.feature_selector.fit_transform(X, y)
                print(f"   Feature selection: {X.shape[1]} -> {X_selected.shape[1]} признаков")
            else:
                X_selected = X

            # 2. Балансировка классов на отобранных признаках
            X_balanced, y_balanced = self._balance_classes_safely(X_selected, y, unique_classes)

            # 3. Масштабирование на финальных данных
            X_scaled = self.ensemble_model.scaler.fit_transform(X_balanced)

            # TimeSeriesSplit для временных рядов
            tscv = TimeSeriesSplit(n_splits=3)  # Меньше splits для малых данных

            # Обучение каждой модели в ансамбле
            model_scores = {}
            successful_models = {}

            for name, model in self.ensemble_model.models.items():
                print(f"   Обучение {name}...")

                try:
                    # Дополнительная защита от segfault для больших данных
                    if name == 'logreg' and len(X_scaled) > 1000:
                        print(f"   {name}: Слишком много данных ({len(X_scaled)}), пропускаем для стабильности")
                        continue

                    # Кросс-валидация с TimeSeriesSplit
                    cv_scores = cross_val_score(model, X_scaled, y_balanced, cv=tscv, scoring='accuracy')

                    # Проверяем на NaN
                    if np.any(np.isnan(cv_scores)):
                        print(f"   {name}: CV дал NaN, пропускаем модель")
                        continue

                    model_scores[name] = np.mean(cv_scores)

                    # Обучение на всех данных
                    model.fit(X_scaled, y_balanced)
                    successful_models[name] = model

                    print(f"   {name}: CV точность = {np.mean(cv_scores):.3f} ± {np.std(cv_scores):.3f}")

                except Exception as e:
                    print(f"   {name}: Ошибка обучения - {e}")
                    continue

            # Обновляем модели только успешными
            if successful_models:
                self.ensemble_model.models = successful_models

                # Сохранение результатов кросс-валидации
                self.ensemble_model.cross_val_scores = list(model_scores.values())

                # Расчет важности признаков (для Random Forest)
                if 'rf' in self.ensemble_model.models:
                    rf_model = self.ensemble_model.models['rf']
                    feature_names = [f'feature_{i}' for i in range(X_scaled.shape[1])]

                    self.ensemble_model.feature_importance = dict(
                        zip(feature_names, rf_model.feature_importances_)
                    )

                self.ensemble_model.is_trained = True

                # Итоговая оценка ансамбля (используем данные напрямую, без feature selection)
                ensemble_predictions = self._predict_ensemble_direct(X_scaled)
                ensemble_accuracy = accuracy_score(y_balanced, ensemble_predictions)

                print(f"✅ Ансамблевая модель обучена!")
                print(f"   Точность ансамбля: {ensemble_accuracy:.3f}")
                if model_scores:
                    best_model = max(model_scores.keys(), key=lambda k: model_scores[k])
                    print(f"   Лучшая модель: {best_model} ({model_scores[best_model]:.3f})")
            else:
                print("❌ Ни одна модель не обучилась успешно")
            
            # Автосохранение после успешного обучения
            if self.config.get('auto_save_enabled', True):
                print("💾 Автосохранение обученных моделей...")
                self.save_ml_models()
                self.save_state()

        except ValueError as e:
            print(f"❌ ML ошибка: {e}")
        except Exception as e:
            print(f"❌ Неожиданная ошибка обучения ансамблевой модели: {e}")
            import traceback
            traceback.print_exc()

    def incremental_train_model(self, new_features: np.ndarray, signal_type: str):
        """Инкрементальное обучение модели на новых данных"""
        if not ML_AVAILABLE or not hasattr(self.ensemble_model, 'incremental_models'):
            return

        try:
            # Преобразуем сигнал в метку
            if signal_type == "bullish_reversal":
                label = 1
            elif signal_type == "bearish_reversal":
                label = -1
            else:
                label = 0

            # Подготавливаем данные
            X = new_features.reshape(1, -1)
            y = np.array([label])

            # Применяем feature selection если есть и обучен
            if (hasattr(self.ensemble_model, 'feature_selector') and
                self.ensemble_model.feature_selector is not None and
                hasattr(self.ensemble_model.feature_selector, 'scores_')):
                try:
                    X_selected = self.ensemble_model.feature_selector.transform(X)
                except Exception as e:
                    print(f"⚠️  Ошибка feature selection в инкрементальном обучении: {e}")
                    X_selected = X
            else:
                # Feature selector не обучен, используем все признаки
                X_selected = X

            # Масштабируем признаки
            if self.ensemble_model.scaler is not None and hasattr(self.ensemble_model.scaler, 'scale_'):
                try:
                    X_scaled = self.ensemble_model.scaler.transform(X_selected)
                except Exception as e:
                    print(f"⚠️  Ошибка масштабирования в инкрементальном обучении: {e}")
                    X_scaled = X_selected
            else:
                # Если scaler не обучен, используем исходные данные
                X_scaled = X_selected

            # Инкрементальное обучение для поддерживающих моделей
            for name, model in self.ensemble_model.incremental_models.items():
                if hasattr(model, 'partial_fit'):
                    # Для первого обучения нужно указать классы
                    if not hasattr(model, 'classes_') or model.classes_ is None:
                        model.partial_fit(X_scaled, y, classes=[-1, 0, 1])
                    else:
                        model.partial_fit(X_scaled, y)

            # Добавляем в буфер обратной связи
            feedback_entry = {
                'timestamp': datetime.now().isoformat(),
                'features': new_features.tolist(),
                'label': label,
                'signal_type': signal_type
            }
            self.ensemble_model.feedback_buffer.append(feedback_entry)

            # Ограничиваем размер буфера
            if len(self.ensemble_model.feedback_buffer) > 100:
                self.ensemble_model.feedback_buffer = self.ensemble_model.feedback_buffer[-100:]

            print(f"✅ Инкрементальное обучение: {signal_type} -> {label}")

        except Exception as e:
            print(f"❌ Ошибка инкрементального обучения: {e}")

    def train_ensemble_model_enhanced(self, features_list: List, labels_list: List[int]):
        """Улучшенное обучение ансамблевой модели с оптимизацией гиперпараметров"""
        if not ML_AVAILABLE or len(features_list) < 50:
            print("⚠️  Недостаточно данных для улучшенного обучения ML модели")
            return

        try:
            print(f"🚀 Запуск улучшенного обучения ML модели на {len(features_list)} образцах...")

            # Сохраняем версию модели перед обучением
            self._save_model_version()

            # Подготовка данных
            X = np.array(features_list)
            y = np.array(labels_list)

            print(f"   Размер данных: {X.shape}")
            print(f"   Уникальные классы: {np.unique(y)}")

            # Балансировка классов
            unique_classes = np.unique(y)
            X_balanced, y_balanced = self._balance_classes_safely(X, y, unique_classes)

            # Feature selection
            if hasattr(self.ensemble_model, 'feature_selector'):
                X_selected = self.ensemble_model.feature_selector.fit_transform(X_balanced, y_balanced)
            else:
                X_selected = X_balanced

            # Масштабирование
            X_scaled = self.ensemble_model.scaler.fit_transform(X_selected)

            # Оптимизация гиперпараметров
            self._optimize_hyperparameters(X_scaled, y_balanced)

            # Обучение с оптимизированными параметрами
            self._train_with_optimized_params(X_scaled, y_balanced)

            # Оценка производительности
            performance = self._evaluate_model_performance(X_scaled, y_balanced)

            # Сохраняем статистику производительности
            self.ensemble_model.performance_history.append({
                'timestamp': datetime.now().isoformat(),
                'performance': performance,
                'data_size': len(features_list),
                'hyperparams': self.ensemble_model.adaptive_hyperparams.copy()
            })

            # Автосохранение
            if self.config.get('auto_save_enabled', True):
                self.save_ml_models()
                self.save_state()

            print(f"✅ Улучшенное обучение завершено! Точность: {performance.get('accuracy', 0):.3f}")

        except Exception as e:
            print(f"❌ Ошибка улучшенного обучения: {e}")
            import traceback
            traceback.print_exc()

    def _save_model_version(self):
        """Сохранение версии модели для возможности отката"""
        try:
            if self.ensemble_model.is_trained:
                version_data = {
                    'timestamp': datetime.now().isoformat(),
                    'models': self.ensemble_model.models.copy() if self.ensemble_model.models else None,
                    'scaler': self.ensemble_model.scaler,
                    'feature_selector': getattr(self.ensemble_model, 'feature_selector', None),
                    'hyperparams': self.ensemble_model.adaptive_hyperparams.copy(),
                    'performance': self.ensemble_model.performance_history[-1] if self.ensemble_model.performance_history else None
                }

                self.ensemble_model.model_versions.append(version_data)

                # Ограничиваем количество сохраненных версий
                if len(self.ensemble_model.model_versions) > 5:
                    self.ensemble_model.model_versions = self.ensemble_model.model_versions[-5:]

                print(f"💾 Сохранена версия модели #{len(self.ensemble_model.model_versions)}")

        except Exception as e:
            print(f"❌ Ошибка сохранения версии модели: {e}")

    def _optimize_hyperparameters(self, X: np.ndarray, y: np.ndarray):
        """Оптимизация гиперпараметров модели"""
        try:
            print("🔧 Оптимизация гиперпараметров...")

            # Простая сеточная оптимизация для RandomForest
            rf_params = {
                'n_estimators': [80, 100, 120],
                'max_depth': [4, 5, 6],
                'min_samples_leaf': [2, 3, 4]
            }

            # Тестируем разные комбинации
            best_score = 0
            best_params = {}

            tscv = TimeSeriesSplit(n_splits=3)

            for n_est in rf_params['n_estimators']:
                for max_d in rf_params['max_depth']:
                    for min_leaf in rf_params['min_samples_leaf']:
                        try:
                            rf_test = RandomForestClassifier(
                                n_estimators=n_est,
                                max_depth=max_d,
                                min_samples_leaf=min_leaf,
                                random_state=42,
                                class_weight='balanced'
                            )

                            scores = cross_val_score(rf_test, X, y, cv=tscv, scoring='accuracy')
                            avg_score = np.mean(scores)

                            if avg_score > best_score:
                                best_score = avg_score
                                best_params = {
                                    'rf_n_estimators': n_est,
                                    'rf_max_depth': max_d,
                                    'rf_min_samples_leaf': min_leaf
                                }
                        except:
                            continue

            # Обновляем гиперпараметры
            if best_params:
                self.ensemble_model.adaptive_hyperparams.update(best_params)
                print(f"   Лучшие параметры RF: {best_params} (точность: {best_score:.3f})")

        except Exception as e:
            print(f"❌ Ошибка оптимизации гиперпараметров: {e}")

    def _train_with_optimized_params(self, X: np.ndarray, y: np.ndarray):
        """Обучение с оптимизированными параметрами"""
        try:
            # Обновляем модели с новыми параметрами
            params = self.ensemble_model.adaptive_hyperparams

            self.ensemble_model.models['rf'] = RandomForestClassifier(
                n_estimators=params.get('rf_n_estimators', 100),
                max_depth=params.get('rf_max_depth', 5),
                min_samples_leaf=params.get('rf_min_samples_leaf', 3),
                random_state=42,
                class_weight='balanced'
            )

            self.ensemble_model.models['logreg'] = LogisticRegression(
                random_state=42,
                class_weight='balanced',
                max_iter=1000,
                C=params.get('logreg_C', 1.0),
                penalty='l2'
            )

            # Обучаем модели
            tscv = TimeSeriesSplit(n_splits=3)
            successful_models = {}

            for name, model in self.ensemble_model.models.items():
                try:
                    scores = cross_val_score(model, X, y, cv=tscv, scoring='accuracy')
                    model.fit(X, y)
                    successful_models[name] = model
                    print(f"   {name}: CV точность = {np.mean(scores):.3f}")
                except Exception as e:
                    print(f"   {name}: Ошибка обучения - {e}")

            if successful_models:
                self.ensemble_model.models = successful_models
                self.ensemble_model.is_trained = True
                print(f"✅ Обучено {len(successful_models)} моделей")

        except Exception as e:
            print(f"❌ Ошибка обучения с оптимизированными параметрами: {e}")

    def _evaluate_model_performance(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """Оценка производительности модели"""
        try:
            if not self.ensemble_model.is_trained:
                return {'accuracy': 0.0}

            # Предсказания ансамбля
            predictions = self._predict_ensemble_direct(X)

            # Базовые метрики
            accuracy = accuracy_score(y, predictions)

            # Метрики по классам
            unique_classes = np.unique(y)
            if len(unique_classes) > 1:
                precision, recall, f1, _ = precision_recall_fscore_support(y, predictions, average='weighted', zero_division=0)
            else:
                precision = recall = f1 = 0.0

            performance = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'n_samples': len(X),
                'n_classes': len(unique_classes)
            }

            return performance

        except Exception as e:
            print(f"❌ Ошибка оценки производительности: {e}")
            return {'accuracy': 0.0}

    def add_signal_feedback(self, signal_timestamp: int, actual_outcome: bool,
                           return_pct: float = 0.0, notes: str = ""):
        """Добавление обратной связи о качестве сигнала"""
        try:
            # Находим соответствующий сигнал
            target_signal = None
            for signal in reversed(self.ai_signals_history):
                if signal.timestamp == signal_timestamp:
                    target_signal = signal
                    break

            if target_signal is None:
                print(f"⚠️  Сигнал с timestamp {signal_timestamp} не найден")
                return

            # Обновляем байесовскую модель
            self.update_bayesian_model(target_signal.signal_type, actual_outcome)

            # Добавляем в буфер обратной связи
            feedback = {
                'timestamp': datetime.now().isoformat(),
                'signal_timestamp': signal_timestamp,
                'signal_type': target_signal.signal_type,
                'predicted_probability': target_signal.probability,
                'predicted_confidence': target_signal.confidence,
                'actual_outcome': actual_outcome,
                'return_pct': return_pct,
                'notes': notes
            }

            self.ensemble_model.feedback_buffer.append(feedback)

            # Обновляем статистику производительности
            self.performance_stats['total_signals'] += 1
            if actual_outcome:
                self.performance_stats['successful_signals'] += 1

            self.performance_stats['win_rate'] = (
                self.performance_stats['successful_signals'] /
                self.performance_stats['total_signals']
            )

            # Проверяем, нужно ли переобучить модель
            self._check_retrain_trigger()

            print(f"✅ Обратная связь добавлена: {target_signal.signal_type} -> {'✓' if actual_outcome else '✗'}")

        except Exception as e:
            print(f"❌ Ошибка добавления обратной связи: {e}")

    def _check_retrain_trigger(self):
        """Проверка необходимости переобучения модели"""
        try:
            if len(self.ensemble_model.feedback_buffer) < 10:
                return

            # Анализируем последние 10 сигналов
            recent_feedback = self.ensemble_model.feedback_buffer[-10:]
            recent_accuracy = sum(1 for f in recent_feedback if f['actual_outcome']) / len(recent_feedback)

            # Если точность упала ниже порога
            if recent_accuracy < (self.bayesian_model.model_accuracy - self.ensemble_model.retrain_threshold):
                print(f"📉 Точность упала до {recent_accuracy:.2%}, запускаем переобучение...")

                # Переобучаем модель с учетом обратной связи
                self._retrain_with_feedback()

        except Exception as e:
            print(f"❌ Ошибка проверки триггера переобучения: {e}")

    def _retrain_with_feedback(self):
        """Переобучение модели с учетом обратной связи"""
        try:
            if len(self.ensemble_model.feedback_buffer) < 20:
                return

            print("🔄 Переобучение модели с учетом обратной связи...")

            # Подготавливаем данные из буфера обратной связи
            feedback_features = []
            feedback_labels = []

            for feedback in self.ensemble_model.feedback_buffer:
                if 'features' in feedback:
                    feedback_features.append(feedback['features'])
                    # Корректируем метки на основе реального результата
                    if feedback['actual_outcome']:
                        if feedback['signal_type'] == 'bullish_reversal':
                            feedback_labels.append(1)
                        elif feedback['signal_type'] == 'bearish_reversal':
                            feedback_labels.append(-1)
                        else:
                            feedback_labels.append(0)
                    else:
                        # Если сигнал был неверным, инвертируем метку
                        if feedback['signal_type'] == 'bullish_reversal':
                            feedback_labels.append(-1)
                        elif feedback['signal_type'] == 'bearish_reversal':
                            feedback_labels.append(1)
                        else:
                            feedback_labels.append(0)

            if len(feedback_features) >= 20:
                # Комбинируем с существующими данными
                all_features = list(self.ml_features_history) + feedback_features
                all_labels = list(self.ml_labels_history) + feedback_labels

                # Переобучаем модель
                self.train_ensemble_model_enhanced(all_features, all_labels)

                # Обновляем timestamp последнего переобучения
                self.ensemble_model.last_retrain_timestamp = datetime.now().isoformat()

                print("✅ Переобучение с обратной связью завершено")

        except Exception as e:
            print(f"❌ Ошибка переобучения с обратной связью: {e}")

    def rollback_to_previous_version(self, version_index: int = -1):
        """Откат к предыдущей версии модели"""
        try:
            if not self.ensemble_model.model_versions:
                print("⚠️  Нет сохраненных версий для отката")
                return False

            if abs(version_index) > len(self.ensemble_model.model_versions):
                print(f"⚠️  Неверный индекс версии: {version_index}")
                return False

            # Получаем версию для отката
            target_version = self.ensemble_model.model_versions[version_index]

            # Восстанавливаем модель
            self.ensemble_model.models = target_version['models']
            self.ensemble_model.scaler = target_version['scaler']
            self.ensemble_model.feature_selector = target_version['feature_selector']
            self.ensemble_model.adaptive_hyperparams = target_version['hyperparams']
            self.ensemble_model.is_trained = True

            print(f"✅ Откат к версии от {target_version['timestamp']}")
            return True

        except Exception as e:
            print(f"❌ Ошибка отката к предыдущей версии: {e}")
            return False

    def _balance_classes_safely(self, X, y, unique_classes):
        """Безопасная балансировка классов с fallback стратегиями"""
        # Подсчитываем количество образцов в каждом классе
        class_counts = {cls: np.sum(y == cls) for cls in unique_classes}
        min_samples = min(class_counts.values())

        print(f"   Распределение классов: {class_counts}")

        # Если у нас достаточно образцов для SMOTE
        if IMBLEARN_AVAILABLE and min_samples >= 6 and len(unique_classes) <= 3:
            try:
                # Ограничиваем размер данных для SMOTE чтобы избежать segfault
                max_samples_for_smote = 1000  # Максимум 1000 образцов для SMOTE

                if len(X) > max_samples_for_smote:
                    print(f"   Слишком много данных для SMOTE ({len(X)}), используем исходные данные")
                    return X, y

                # Используем более консервативные настройки SMOTE
                smote = SMOTE(
                    random_state=42,
                    k_neighbors=min(5, min_samples - 1),  # Адаптивное количество соседей
                    sampling_strategy='auto'
                )
                X_balanced, y_balanced = smote.fit_resample(X, y)
                print(f"   SMOTE балансировка: {len(X)} -> {len(X_balanced)} образцов")
                return X_balanced, y_balanced
            except Exception as e:
                print(f"   SMOTE не удался: {e}, используем исходные данные")

        # Fallback: используем исходные данные с class_weight='balanced' в моделях
        print(f"   Используем исходные данные с весами классов")
        return X, y

    def _predict_ensemble_direct(self, features):
        """Прямое предсказание ансамбля без дополнительной обработки (для обучения)"""
        if not ML_AVAILABLE or not self.ensemble_model.is_trained:
            return np.array([0] * len(features))

        try:
            # Получение предсказаний от каждой модели напрямую
            predictions = {}
            probabilities = {}

            for name, model in self.ensemble_model.models.items():
                pred = model.predict(features)
                prob = model.predict_proba(features)

                predictions[name] = pred
                probabilities[name] = prob

            # Ансамблевое голосование
            ensemble_probs = np.mean(list(probabilities.values()), axis=0)

            # Получаем классы из первой модели
            first_model = list(self.ensemble_model.models.values())[0]
            classes = first_model.classes_

            # Предсказания на основе максимальной вероятности
            ensemble_predictions = classes[np.argmax(ensemble_probs, axis=1)]

            return ensemble_predictions

        except Exception as e:
            print(f"❌ Ошибка прямого предсказания ансамбля: {e}")
            return np.array([0] * len(features))

    def predict_ensemble(self, features):
        """Предсказание ансамблевой модели с правильным маппингом классов"""
        if not ML_AVAILABLE or not self.ensemble_model.is_trained:
            return np.array([0] * len(features))

        try:
            # Подготавливаем признаки в том же порядке, что и при обучении:
            # 1. Feature selection
            if hasattr(self.ensemble_model, 'feature_selector') and self.ensemble_model.feature_selector is not None:
                features_selected = self.ensemble_model.feature_selector.transform(features)
            else:
                features_selected = features

            # 2. Масштабирование
            features_scaled = self.ensemble_model.scaler.transform(features_selected)

            # Получение предсказаний от каждой модели
            predictions = {}
            probabilities = {}

            for name, model in self.ensemble_model.models.items():
                pred = model.predict(features_scaled)
                prob = model.predict_proba(features_scaled)

                predictions[name] = pred
                probabilities[name] = prob

            # Ансамблевое голосование (мягкое голосование по вероятностям)
            ensemble_probs = np.mean(list(probabilities.values()), axis=0)

            # Получаем классы из первой модели (все модели должны иметь одинаковые классы)
            first_model = list(self.ensemble_model.models.values())[0]
            classes = first_model.classes_

            # Предсказания на основе максимальной вероятности
            ensemble_predictions = classes[np.argmax(ensemble_probs, axis=1)]

            return ensemble_predictions

        except Exception as e:
            print(f"❌ Ошибка предсказания ансамбля: {e}")
            return np.array([0] * len(features))

    def get_ensemble_confidence(self, features) -> float:
        """Получение уверенности ансамблевой модели с feature selection"""
        if not ML_AVAILABLE or not self.ensemble_model.is_trained:
            return 0.5

        try:
            # Подготавливаем признаки в том же порядке, что и при обучении:
            features_reshaped = features.reshape(1, -1)

            # 1. Feature selection
            if hasattr(self.ensemble_model, 'feature_selector') and self.ensemble_model.feature_selector is not None:
                features_selected = self.ensemble_model.feature_selector.transform(features_reshaped)
            else:
                features_selected = features_reshaped

            # 2. Масштабирование
            features_scaled = self.ensemble_model.scaler.transform(features_selected)

            # Получение вероятностей от каждой модели
            all_probs = []
            trained_models = []

            for name, model in self.ensemble_model.models.items():
                try:
                    # Проверяем, обучена ли модель
                    if hasattr(model, 'classes_') and model.classes_ is not None:
                        probs = model.predict_proba(features_scaled)[0]
                        all_probs.append(probs)
                        trained_models.append(name)
                    else:
                        print(f"⚠️  Модель {name} не обучена, пропускаем в расчете уверенности")
                except Exception as e:
                    print(f"❌ Ошибка модели {name} в расчете уверенности: {e}")
                    continue

            if not all_probs:
                print("⚠️  Нет обученных моделей для расчета уверенности")
                return 0.5

            # Средние вероятности
            mean_probs = np.mean(all_probs, axis=0)

            # Максимальная вероятность как уверенность
            confidence = np.max(mean_probs)

            # Дополнительный бонус за согласованность моделей
            agreement_bonus = 1.0 - np.std([np.max(probs) for probs in all_probs])

            return min(0.99, confidence * agreement_bonus)

        except Exception as e:
            print(f"❌ Ошибка расчета уверенности ансамбля: {e}")
            return 0.5

    def _create_ml_features(self, ohlc: Dict[str, List[float]], volume: List[float],
                           evidence: Dict[str, Any]):
        """Создание оптимизированных признаков для ML модели (максимум 30 признаков)"""
        features = []

        if len(ohlc['close']) < 20:
            return np.array([0] * 30)  # Возвращаем нулевой вектор

        closes = np.array(ohlc['close'][-20:])
        highs = np.array(ohlc['high'][-20:])
        lows = np.array(ohlc['low'][-20:])

        # 1. Ключевые ценовые признаки (5 признаков)
        features.extend([
            (closes[-1] - closes[0]) / closes[0],  # Общий ROC за период
            (closes[-1] - np.mean(closes)) / np.std(closes) if np.std(closes) > 0 else 0,  # Z-score
            np.std(closes) / np.mean(closes) if np.mean(closes) > 0 else 0,  # Коэффициент вариации
            (np.max(highs) - np.min(lows)) / np.mean(closes) if np.mean(closes) > 0 else 0,  # Диапазон
            (closes[-1] - closes[-5]) / closes[-5] if len(closes) >= 5 else 0  # Краткосрочный ROC
        ])

        # 2. Основные технические индикаторы (6 признаков)
        rsi = evidence.get('rsi', 50)
        features.extend([
            rsi / 100,  # RSI нормализованный
            1 if rsi < 30 else 0,  # RSI перепроданность
            1 if rsi > 70 else 0,  # RSI перекупленность
            evidence.get('macd_histogram', 0),  # MACD гистограмма
            evidence.get('bb_position', 0.5),  # Позиция в Bollinger Bands
            evidence.get('mfi', 50) / 100  # Money Flow Index
        ])

        # 3. EMA-200 фильтр и тренд (2 признака)
        ema_trend = evidence.get('ema_trend', 0)
        features.extend([
            ema_trend,  # EMA-200 тренд (-1, 0, 1)
            1 if ema_trend != 0 else 0  # Наличие четкого тренда
        ])

        # 4. Объемные признаки (3 признака)
        volume_spike = evidence.get('volume_spike', 0)
        volume_ratio = evidence.get('volume_analysis', {}).get('ratio', 1.0)
        features.extend([
            volume_spike,  # Объемный всплеск (бинарный)
            min(volume_ratio, 5.0) / 5.0,  # Нормализованное отношение объема (макс 5x)
            1 if volume_ratio >= 2.0 else 0  # Сильный объемный всплеск
        ])

        # 5. Дивергенции (4 признака) - самые важные для разворотов
        features.extend([
            1 if evidence.get('macd_divergence') == 'bullish' else 0,
            1 if evidence.get('macd_divergence') == 'bearish' else 0,
            1 if evidence.get('rsi_divergence') == 'bullish' else 0,
            1 if evidence.get('rsi_divergence') == 'bearish' else 0
        ])

        # 6. Ключевые свечные паттерны (4 признака)
        patterns = evidence.get('candle_patterns', [])
        features.extend([
            1 if 'hammer' in patterns else 0,
            1 if 'shooting_star' in patterns else 0,
            1 if 'bullish_engulfing' in patterns else 0,
            1 if 'bearish_engulfing' in patterns else 0
        ])

        # 7. Поддержка/сопротивление (2 признака)
        features.extend([
            1 if evidence.get('near_support') else 0,
            1 if evidence.get('near_resistance') else 0
        ])

        # 8. Экстремальные значения индикаторов (4 признака)
        stoch_k = evidence.get('stoch_k', 50)
        williams_r = evidence.get('williams_r', -50)
        features.extend([
            1 if stoch_k < 20 else 0,  # Stochastic перепроданность
            1 if stoch_k > 80 else 0,  # Stochastic перекупленность
            1 if williams_r < -80 else 0,  # Williams %R перепроданность
            1 if williams_r > -20 else 0   # Williams %R перекупленность
        ])

        # Проверяем, что у нас ровно 30 признаков
        assert len(features) == 30, f"Ожидалось 30 признаков, получено {len(features)}"

        return np.array(features)

    def calculate_enhanced_bayesian_probability(self, evidence: Dict[str, Any],
                                              ensemble_confidence: float) -> Tuple[float, str]:
        """Улучшенный расчет байесовской вероятности с учетом ML"""
        # Получаем базовую байесовскую вероятность
        base_prob, base_signal = self.calculate_bayesian_probability(evidence)

        # Корректируем с учетом ML модели
        ml_weight = 0.3  # Вес ML модели в итоговом решении
        bayesian_weight = 0.7  # Вес байесовской модели

        # Если ML модель обучена и уверена
        if ensemble_confidence > 0.7:
            # ML усиливает сигнал
            enhanced_prob = base_prob * bayesian_weight + ensemble_confidence * 100 * ml_weight
        else:
            # Используем только байесовскую модель
            enhanced_prob = base_prob

        # Ограничиваем вероятность
        enhanced_prob = min(95.0, max(5.0, enhanced_prob))

        return enhanced_prob, base_signal

    def calculate_bayesian_probability(self, evidence: Dict[str, Any]) -> Tuple[float, str]:
        """Расчет байесовской вероятности"""
        # Базовые приоры
        prior_bull = self.bayesian_model.prior_bullish
        prior_bear = self.bayesian_model.prior_bearish
        
        # Likelihood на основе технических индикаторов
        likelihood_bull = 0.5
        likelihood_bear = 0.5
        
        # MACD дивергенция
        if evidence.get('macd_divergence') == 'bullish':
            likelihood_bull *= 1.8
        elif evidence.get('macd_divergence') == 'bearish':
            likelihood_bear *= 1.8
        
        # RSI дивергенция
        if evidence.get('rsi_divergence') == 'bullish':
            likelihood_bull *= 1.6
        elif evidence.get('rsi_divergence') == 'bearish':
            likelihood_bear *= 1.6
        
        # Свечные паттерны
        patterns = evidence.get('candle_patterns', [])
        bullish_patterns = ['hammer', 'bullish_engulfing']
        bearish_patterns = ['shooting_star', 'bearish_engulfing']
        
        for pattern in patterns:
            if pattern in bullish_patterns:
                likelihood_bull *= 1.4
            elif pattern in bearish_patterns:
                likelihood_bear *= 1.4
        
        # Объемное подтверждение
        volume_analysis = evidence.get('volume_analysis', {})
        if volume_analysis.get('spike', False):
            volume_strength = volume_analysis.get('strength', 0) / 100
            likelihood_bull *= (1 + volume_strength * 0.5)
            likelihood_bear *= (1 + volume_strength * 0.5)
        
        # Уровни поддержки/сопротивления
        near_support = evidence.get('near_support', False)
        near_resistance = evidence.get('near_resistance', False)

        if near_support:
            likelihood_bull *= 1.3
        if near_resistance:
            likelihood_bear *= 1.3

        # === EMA-200 фильтр (новый критический фактор) ===
        ema_trend = evidence.get('ema_trend', 0)
        if ema_trend == 1:  # Цена выше EMA-200
            likelihood_bull *= 1.2  # Бонус к бычьим сигналам
        elif ema_trend == -1:  # Цена ниже EMA-200
            likelihood_bear *= 1.2  # Бонус к медвежьим сигналам

        # === Улучшенный объемный анализ ===
        volume_spike = evidence.get('volume_spike', 0)
        volume_ratio = evidence.get('volume_ratio', 1.0)

        if volume_spike:
            # Объемный всплеск усиливает любой сигнал
            volume_multiplier = min(1.5, 1.0 + (volume_ratio - 1.5) * 0.2)
            likelihood_bull *= volume_multiplier
            likelihood_bear *= volume_multiplier

        # Расширенные технические индикаторы
        enhanced_features = evidence.get('enhanced_features', {})

        # Bollinger Bands
        bb_position = enhanced_features.get('bb_position', 0.5)
        if bb_position < 0.1:  # Близко к нижней полосе
            likelihood_bull *= 1.4
        elif bb_position > 0.9:  # Близко к верхней полосе
            likelihood_bear *= 1.4

        # Stochastic
        stoch_k = enhanced_features.get('stoch_k', 50)
        if stoch_k < 20:  # Перепроданность
            likelihood_bull *= 1.3
        elif stoch_k > 80:  # Перекупленность
            likelihood_bear *= 1.3

        # Williams %R
        williams_r = enhanced_features.get('williams_r', -50)
        if williams_r < -80:  # Перепроданность
            likelihood_bull *= 1.2
        elif williams_r > -20:  # Перекупленность
            likelihood_bear *= 1.2

        # Money Flow Index
        mfi = enhanced_features.get('mfi', 50)
        if mfi < 20:  # Перепроданность по объему
            likelihood_bull *= 1.5
        elif mfi > 80:  # Перекупленность по объему
            likelihood_bear *= 1.5

        # Rate of Change
        roc_5 = enhanced_features.get('roc_5', 0)
        roc_10 = enhanced_features.get('roc_10', 0)

        # Дивергенция в ROC
        if roc_5 > 0 and roc_10 < 0:  # Краткосрочный рост, долгосрочное падение
            likelihood_bull *= 1.2
        elif roc_5 < 0 and roc_10 > 0:  # Краткосрочное падение, долгосрочный рост
            likelihood_bear *= 1.2

        # ADX для силы тренда
        adx = enhanced_features.get('adx', 0)
        if adx > 25:  # Сильный тренд
            # Усиливаем сигналы при сильном тренде
            likelihood_bull *= 1.1
            likelihood_bear *= 1.1
        
        # Расчет posterior probability
        posterior_bull = prior_bull * likelihood_bull
        posterior_bear = prior_bear * likelihood_bear

        # === Байесовская регуляризация ===
        # Добавляем небольшую дисперсию для избежания переобучения на единичных аномалиях
        regularization = self.bayesian_model.regularization_factor
        posterior_bull += regularization * prior_bull
        posterior_bear += regularization * prior_bear

        # Нормализация
        total_posterior = posterior_bull + posterior_bear
        if total_posterior > 0:
            prob_bull = posterior_bull / total_posterior
            prob_bear = posterior_bear / total_posterior
        else:
            prob_bull = prob_bear = 0.5
        
        # Адаптивные пороги для определения сигнала
        signal_threshold = 0.55  # Снижено для генерации большего количества сигналов

        # Повышаем пороги по мере накопления опыта
        if self.bayesian_model.training_samples > 50:
            signal_threshold = 0.58
        if self.bayesian_model.training_samples > 100:
            signal_threshold = 0.62

        # === BOOTSTRAP СИГНАЛЫ ДЛЯ ПЕРВЫХ ОБРАЗЦОВ ===
        # Дайте модели первые "семена" сигналов для обучения
        if self.bayesian_model.training_samples < 55:
            # Мягкий порог для генерации первых сигналов
            bootstrap_threshold = 0.52
            if prob_bull > prob_bear and prob_bull > bootstrap_threshold:
                return prob_bull * 100, "bullish_reversal"
            elif prob_bear > prob_bull and prob_bear > bootstrap_threshold:
                return prob_bear * 100, "bearish_reversal"

        # Определение направления и вероятности (обычная логика)
        if prob_bull > prob_bear and prob_bull > signal_threshold:
            return prob_bull * 100, "bullish_reversal"
        elif prob_bear > prob_bull and prob_bear > signal_threshold:
            return prob_bear * 100, "bearish_reversal"
        else:
            return max(prob_bull, prob_bear) * 100, "neutral"
    
    def calculate_ai_confidence(self, evidence: Dict[str, Any], probability: float) -> float:
        """Расчет уверенности AI"""
        base_confidence = min(probability, 85.0)  # Базовая уверенность
        
        # Бонусы за качество сигнала
        confidence_bonus = 0
        
        # Множественные подтверждения
        confirmations = 0
        if evidence.get('macd_divergence') != 'none':
            confirmations += 1
        if evidence.get('rsi_divergence') != 'none':
            confirmations += 1
        if evidence.get('candle_patterns'):
            confirmations += 1
        if evidence.get('volume_analysis', {}).get('spike', False):
            confirmations += 1
        if evidence.get('near_support') or evidence.get('near_resistance'):
            confirmations += 1
        
        confidence_bonus += confirmations * 5
        
        # Точность модели
        model_accuracy_bonus = self.bayesian_model.model_accuracy * 20
        
        # Количество обучающих образцов
        training_bonus = min(self.bayesian_model.training_samples / 100 * 10, 10)
        
        final_confidence = base_confidence + confidence_bonus + model_accuracy_bonus + training_bonus
        
        return min(final_confidence, 95.0)  # Максимум 95%
    
    def calculate_mathematical_expectation(self, probability: float, confidence: float) -> float:
        """Расчет математического ожидания"""
        # Базовые параметры
        win_rate = probability / 100
        loss_rate = 1 - win_rate
        
        # Средний выигрыш и проигрыш на основе R:R
        avg_win = self.config['risk_reward_ratio']
        avg_loss = -1.0
        
        # Корректировка на уверенность
        confidence_factor = confidence / 100
        adjusted_win_rate = win_rate * confidence_factor
        adjusted_loss_rate = 1 - adjusted_win_rate
        
        # Математическое ожидание
        expectation = (adjusted_win_rate * avg_win) + (adjusted_loss_rate * avg_loss)
        
        return expectation
    
    def calculate_risk_score(self, evidence: Dict[str, Any], confidence: float) -> float:
        """Расчет оценки риска"""
        base_risk = 50.0  # Базовый риск
        
        # Снижение риска при высокой уверенности
        confidence_reduction = (confidence - 50) * 0.3
        
        # Снижение риска при объемном подтверждении
        volume_reduction = 0
        if evidence.get('volume_analysis', {}).get('spike', False):
            volume_reduction = 10
        
        # Снижение риска при множественных подтверждениях
        confirmations = len([x for x in [
            evidence.get('macd_divergence') != 'none',
            evidence.get('rsi_divergence') != 'none',
            bool(evidence.get('candle_patterns')),
            evidence.get('near_support') or evidence.get('near_resistance')
        ] if x])
        
        confirmation_reduction = confirmations * 5
        
        final_risk = base_risk - confidence_reduction - volume_reduction - confirmation_reduction
        
        return max(5.0, min(final_risk, 95.0))  # Риск от 5% до 95%
    
    def process_bar(self, ohlc: Dict[str, List[float]], volume: List[float], bar_index: int) -> AISignal:
        """Обработка нового бара и генерация AI сигнала"""
        if len(ohlc['close']) < 50:  # Минимум данных для анализа
            return AISignal(
                signal_type="none",
                probability=0.0,
                confidence=0.0,
                strength=0,
                math_expectation=0.0,
                risk_score=50.0,
                reasoning="Недостаточно данных для анализа",
                is_valid=False,
                timestamp=bar_index
            )
        
        # Расчет технических индикаторов
        macd, macd_signal, macd_histogram = self.calculate_macd(ohlc['close'])
        rsi = self.calculate_rsi(ohlc['close'])
        atr = self.calculate_atr(ohlc['high'], ohlc['low'], ohlc['close'])

        # Расчет расширенных признаков
        enhanced_features = self.calculate_enhanced_features(ohlc, volume)
        
        # Сохранение истории
        self.macd_history.append({
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram,
            'bar_index': bar_index
        })
        
        self.rsi_history.append({
            'rsi': rsi,
            'bar_index': bar_index
        })
        
        self.atr_history.append({
            'atr': atr,
            'bar_index': bar_index
        })
        
        # Обнаружение дивергенций
        macd_values = [h['macd'] for h in self.macd_history]
        rsi_values = [h['rsi'] for h in self.rsi_history]
        
        macd_divergence = self.detect_macd_divergence(ohlc['close'], macd_values)
        rsi_divergence = self.detect_rsi_divergence(ohlc['close'], rsi_values)
        
        # Обнаружение свечных паттернов
        candle_patterns = self.detect_candle_patterns(ohlc)
        
        # Обновление уровней поддержки/сопротивления
        self.update_support_resistance(ohlc, bar_index)
        
        # Анализ объема
        volume_analysis = self.analyze_volume(volume)
        
        # Проверка близости к уровням S/R
        current_price = ohlc['close'][-1]
        near_support = any(abs(level.price - current_price) / current_price < 0.005 
                          for level in self.support_levels)
        near_resistance = any(abs(level.price - current_price) / current_price < 0.005 
                             for level in self.resistance_levels)
        
        # Сбор evidence для байесовского анализа
        evidence = {
            'macd_divergence': macd_divergence,
            'rsi_divergence': rsi_divergence,
            'candle_patterns': candle_patterns,
            'volume_analysis': volume_analysis,
            'near_support': near_support,
            'near_resistance': near_resistance,
            'rsi': rsi,
            'macd_histogram': macd_histogram,
            # Добавляем расширенные признаки
            'enhanced_features': enhanced_features
        }

        # Обновляем evidence расширенными признаками
        evidence.update(enhanced_features)

        # Создание ML признаков
        ml_features = self._create_ml_features(ohlc, volume, evidence)

        # Получение предсказания ансамблевой модели
        ensemble_confidence = self.get_ensemble_confidence(ml_features)

        # Расчет байесовской вероятности с учетом ML
        probability, signal_type = self.calculate_enhanced_bayesian_probability(evidence, ensemble_confidence)
        
        # Расчет уверенности AI
        confidence = self.calculate_ai_confidence(evidence, probability)
        
        # Расчет математического ожидания
        math_expectation = self.calculate_mathematical_expectation(probability, confidence)
        
        # Расчет оценки риска
        risk_score = self.calculate_risk_score(evidence, confidence)
        
        # Расчет силы сигнала
        strength = min(100, int(probability * 0.7 + confidence * 0.3))
        
        # Генерация обоснования
        reasoning = self._generate_reasoning(evidence, probability, confidence)
        
        # Улучшенная проверка валидности сигнала с учетом ML
        is_valid = self._enhanced_signal_validation(
            probability, confidence, strength, math_expectation, evidence, ensemble_confidence
        )

        # Сохранение данных для обучения ML модели
        if len(self.ml_features_history) < 1000:  # Ограничиваем размер
            self.ml_features_history.append(ml_features)
            # Создаем метку на основе сигнала (упрощенная логика)
            if signal_type == "bullish_reversal":
                self.ml_labels_history.append(1)
            elif signal_type == "bearish_reversal":
                self.ml_labels_history.append(-1)
            else:
                self.ml_labels_history.append(0)

            # Увеличиваем счетчик обучающих образцов для байесовской модели
            # Это позволяет валидации работать даже без реальной обратной связи
            if signal_type != "none":
                self.bayesian_model.training_samples += 1

        # Улучшенное периодическое переобучение ML модели
        # Инкрементальное обучение каждые 10 образцов, полное переобучение каждые 50
        if len(self.ml_features_history) >= 30:
            if len(self.ml_features_history) % 10 == 0:
                # Инкрементальное обучение
                self.incremental_train_model(ml_features, signal_type)
            elif len(self.ml_features_history) % 50 == 0:
                # Полное переобучение с оптимизацией
                self.train_ensemble_model_enhanced(list(self.ml_features_history), list(self.ml_labels_history))
        
        # Создание AI сигнала
        ai_signal = AISignal(
            signal_type=signal_type,
            probability=probability,
            confidence=confidence,
            strength=strength,
            math_expectation=math_expectation,
            risk_score=risk_score,
            reasoning=reasoning,
            is_valid=is_valid,
            timestamp=bar_index
        )
        
        # Сохранение в историю
        self.ai_signals_history.append(ai_signal)
        
        # Проверка автосохранения
        self._check_auto_save(bar_index)
        
        return ai_signal

    def _enhanced_signal_validation(self, probability: float, confidence: float,
                                   strength: int, math_expectation: float,
                                   evidence: Dict[str, Any], ensemble_confidence: float = 0.5) -> bool:
        """Улучшенная валидация сигнала с максимально мягкими критериями для обучения"""

        # === МАКСИМАЛЬНО МЯГКИЕ КРИТЕРИИ ДЛЯ ОБУЧЕНИЯ ===
        # Для первых 2000 образцов используем максимально мягкие критерии
        early_stage = self.bayesian_model.training_samples < 2000

        # Подсчет подтверждений (но не требуем их обязательно)
        confirmations = 0

        # 1. EMA-200 фильтр (желательный)
        ema_trend = evidence.get('ema_trend', 0)
        if ema_trend != 0:
            confirmations += 1

        # 2. Объемный всплеск (желательный)
        volume_spike = evidence.get('volume_spike', 0)
        if volume_spike == 1:
            confirmations += 1

        # 3. Дивергенция (важная)
        has_divergence = (evidence.get('macd_divergence') != 'none' or
                         evidence.get('rsi_divergence') != 'none')
        if has_divergence:
            confirmations += 1

        # 4. Близость к S/R уровням (желательно)
        near_sr = evidence.get('near_support') or evidence.get('near_resistance')
        if near_sr:
            confirmations += 1

        # 5. Любые технические индикаторы в экстремальных зонах
        enhanced_features = evidence.get('enhanced_features', {})

        # Bollinger Bands экстремумы (мягкие критерии)
        bb_position = enhanced_features.get('bb_position', 0.5)
        if bb_position < 0.3 or bb_position > 0.7:  # Еще мягче
            confirmations += 1

        # Stochastic экстремумы (мягкие критерии)
        stoch_k = enhanced_features.get('stoch_k', 50)
        if stoch_k < 40 or stoch_k > 60:  # Еще мягче
            confirmations += 1

        # RSI экстремумы (добавляем новый критерий)
        if hasattr(self, 'rsi_history') and self.rsi_history:
            current_rsi = self.rsi_history[-1]['rsi']
            if current_rsi < 45 or current_rsi > 55:  # Еще мягче
                confirmations += 1

        # === МАКСИМАЛЬНО МЯГКИЕ БАЗОВЫЕ ТРЕБОВАНИЯ ===
        if early_stage:
            # Очень мягкие критерии для обучения
            min_probability = 50.0    # Максимально мягко
            min_confidence = 50.0     # Максимально мягко
            min_strength = 40         # Максимально мягко
            min_math_expectation = -0.2  # Разрешаем отрицательные
            min_model_accuracy = 0.30  # Очень мягко
            required_confirmations = 0  # Не требуем подтверждений
        elif self.bayesian_model.training_samples <= 500:
            min_probability = 55.0    # Мягко
            min_confidence = 55.0     # Мягко
            min_strength = 45         # Мягко
            min_math_expectation = -0.1
            min_model_accuracy = 0.40
            required_confirmations = 0  # Не требуем подтверждений
        elif self.bayesian_model.training_samples <= 1000:
            min_probability = 60.0    # Средне
            min_confidence = 60.0     # Средне
            min_strength = 50         # Средне
            min_math_expectation = 0.0
            min_model_accuracy = 0.45
            required_confirmations = 1  # Требуем 1 подтверждение
        else:
            min_probability = 65.0    # Нормальные критерии
            min_confidence = 65.0     # Нормальные критерии
            min_strength = 55         # Нормальные критерии
            min_math_expectation = 0.05
            min_model_accuracy = 0.50
            required_confirmations = 1  # Требуем 1 подтверждение

        # Проверка подтверждений (только если требуются)
        if confirmations < required_confirmations:
            return False

        # Базовые проверки
        if probability < min_probability:
            return False
        if confidence < min_confidence:
            return False
        if strength < min_strength:
            return False
        if math_expectation < min_math_expectation:
            return False

        # === ПРОВЕРКА КАЧЕСТВА МОДЕЛИ (очень мягкая) ===
        if self.bayesian_model.model_accuracy < min_model_accuracy:
            return False

        # Минимальное количество обучающих данных (практически без ограничений)
        if self.bayesian_model.training_samples < 1:
            return False

        # === ML УВЕРЕННОСТЬ (бонус) ===
        if ML_AVAILABLE and self.ensemble_model.is_trained:
            if ensemble_confidence > 0.55:  # Снижен порог
                # Высокая ML уверенность - принимаем сигнал
                return True
            elif ensemble_confidence < 0.35:  # Снижен порог
                return False

        return True

    def _generate_reasoning(self, evidence: Dict[str, Any], probability: float, confidence: float) -> str:
        """Генерация обоснования сигнала"""
        reasons = []
        
        if evidence['macd_divergence'] != 'none':
            reasons.append(f"MACD дивергенция: {evidence['macd_divergence']}")
        
        if evidence['rsi_divergence'] != 'none':
            reasons.append(f"RSI дивергенция: {evidence['rsi_divergence']}")
        
        if evidence['candle_patterns']:
            patterns_str = ", ".join(evidence['candle_patterns'])
            reasons.append(f"Свечные паттерны: {patterns_str}")
        
        if evidence['volume_analysis']['spike']:
            reasons.append(f"Всплеск объема: {evidence['volume_analysis']['ratio']:.1f}x")
        
        if evidence['near_support']:
            reasons.append("Близость к поддержке")
        
        if evidence['near_resistance']:
            reasons.append("Близость к сопротивлению")
        
        if not reasons:
            reasons.append("Статистический анализ")
        
        return "; ".join(reasons)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Получение статистики производительности"""
        if not self.ai_signals_history:
            return self.performance_stats
        
        valid_signals = [s for s in self.ai_signals_history if s.is_valid]
        
        self.performance_stats.update({
            'total_signals': len(valid_signals),
            'avg_probability': np.mean([s.probability for s in valid_signals]) if valid_signals else 0,
            'avg_confidence': np.mean([s.confidence for s in valid_signals]) if valid_signals else 0,
            'avg_math_expectation': np.mean([s.math_expectation for s in valid_signals]) if valid_signals else 0,
            'model_accuracy': self.bayesian_model.model_accuracy * 100,
            'training_samples': self.bayesian_model.training_samples
        })
        
        return self.performance_stats
    
    def save_ml_models(self, filepath: Optional[str] = None):
        """Сохранение обученных ML моделей"""
        if not ML_AVAILABLE:
            print("⚠️  ML библиотеки недоступны, сохранение моделей невозможно")
            return
        
        if filepath is None:
            # Создаем директорию если не существует
            models_dir = self.config.get('models_dir', 'ai_models')
            os.makedirs(models_dir, exist_ok=True)
            filepath = os.path.join(models_dir, self.config.get('ml_models_file', 'ml_ensemble_models.pkl'))
        
        try:
            models_data = {
                'models': self.ensemble_model.models,
                'scaler': self.ensemble_model.scaler,
                'is_trained': self.ensemble_model.is_trained,
                'feature_importance': self.ensemble_model.feature_importance,
                'cross_val_scores': self.ensemble_model.cross_val_scores,
                'feature_selector': getattr(self.ensemble_model, 'feature_selector', None),
                'training_samples_count': len(self.ml_features_history),
                'timestamp': datetime.now().isoformat(),
                # Новые поля для улучшенного дообучения
                'incremental_models': getattr(self.ensemble_model, 'incremental_models', {}),
                'adaptive_hyperparams': getattr(self.ensemble_model, 'adaptive_hyperparams', {}),
                'performance_history': getattr(self.ensemble_model, 'performance_history', []),
                'feedback_buffer': getattr(self.ensemble_model, 'feedback_buffer', []),
                'last_retrain_timestamp': getattr(self.ensemble_model, 'last_retrain_timestamp', None),
                'retrain_threshold': getattr(self.ensemble_model, 'retrain_threshold', 0.05)
            }
            
            if JOBLIB_AVAILABLE:
                joblib.dump(models_data, filepath)
            else:
                with open(filepath, 'wb') as f:
                    pickle.dump(models_data, f)
            
            print(f"✅ ML модели сохранены в: {filepath}")
            
        except Exception as e:
            print(f"❌ Ошибка сохранения ML моделей: {e}")
    
    def load_ml_models(self, filepath: Optional[str] = None):
        """Загрузка обученных ML моделей"""
        if not ML_AVAILABLE:
            print("⚠️  ML библиотеки недоступны, загрузка моделей невозможна")
            return False
        
        if filepath is None:
            models_dir = self.config.get('models_dir', 'ai_models')
            filepath = os.path.join(models_dir, self.config.get('ml_models_file', 'ml_ensemble_models.pkl'))
        
        if not os.path.exists(filepath):
            print(f"⚠️  Файл ML моделей не найден: {filepath}")
            return False
        
        try:
            if JOBLIB_AVAILABLE:
                models_data = joblib.load(filepath)
            else:
                with open(filepath, 'rb') as f:
                    models_data = pickle.load(f)
            
            # Валидация загруженных моделей
            if not self._validate_loaded_models(models_data):
                print("❌ Загруженные модели не прошли валидацию")
                return False
            
            # Восстановление моделей
            self.ensemble_model.models = models_data['models']

            # Восстанавливаем отсутствующие модели
            self._restore_missing_models()

            # Принудительно переобучаем модель если есть восстановленные модели
            if len(self.ml_features_history) >= 50:
                print("🔄 Принудительное переобучение после восстановления моделей...")
                self.train_ensemble_model_enhanced(list(self.ml_features_history), list(self.ml_labels_history))
            self.ensemble_model.scaler = models_data['scaler']
            self.ensemble_model.is_trained = models_data['is_trained']
            self.ensemble_model.feature_importance = models_data.get('feature_importance', {})
            self.ensemble_model.cross_val_scores = models_data.get('cross_val_scores', [])
            
            if 'feature_selector' in models_data and models_data['feature_selector'] is not None:
                self.ensemble_model.feature_selector = models_data['feature_selector']

            # Загружаем новые поля для улучшенного дообучения
            self.ensemble_model.incremental_models = models_data.get('incremental_models', {})
            self.ensemble_model.adaptive_hyperparams = models_data.get('adaptive_hyperparams', {})
            self.ensemble_model.performance_history = models_data.get('performance_history', [])
            self.ensemble_model.feedback_buffer = models_data.get('feedback_buffer', [])
            self.ensemble_model.last_retrain_timestamp = models_data.get('last_retrain_timestamp')
            self.ensemble_model.retrain_threshold = models_data.get('retrain_threshold', 0.05)

            print(f"✅ ML модели загружены из: {filepath}")
            print(f"   Дата обучения: {models_data.get('timestamp', 'неизвестно')}")
            print(f"   Количество обучающих образцов: {models_data.get('training_samples_count', 'неизвестно')}")
            print(f"   Последнее переобучение: {self.ensemble_model.last_retrain_timestamp or 'никогда'}")
            print(f"   Буфер обратной связи: {len(self.ensemble_model.feedback_buffer)} записей")
            
            return True
            
        except Exception as e:
            print(f"❌ Ошибка загрузки ML моделей: {e}")
            return False
    
    def _validate_loaded_models(self, models_data: Dict) -> bool:
        """Валидация загруженных ML моделей"""
        required_keys = ['models', 'scaler', 'is_trained']
        
        # Проверка наличия обязательных ключей
        for key in required_keys:
            if key not in models_data:
                print(f"❌ Отсутствует обязательный ключ: {key}")
                return False
        
        # Проверка что модели обучены
        if not models_data['is_trained']:
            print("❌ Модели не обучены")
            return False
        
        # Проверка типов моделей
        if not isinstance(models_data['models'], dict):
            print("❌ Неверный формат моделей")
            return False
        
        # Проверка наличия основных моделей и восстановление отсутствующих
        expected_models = ['rf', 'logreg']
        missing_models = []

        for model_name in expected_models:
            if model_name not in models_data['models'] or models_data['models'][model_name] is None:
                missing_models.append(model_name)
                print(f"⚠️  Отсутствует модель: {model_name}")

        if missing_models:
            print(f"🔧 Будут восстановлены отсутствующие модели: {missing_models}")
            # Не возвращаем False, а позволяем загрузиться с восстановлением

        return True

    def _restore_missing_models(self):
        """Восстановление отсутствующих моделей"""
        try:
            if not hasattr(self.ensemble_model, 'models') or self.ensemble_model.models is None:
                self.ensemble_model.models = {}

            # Восстанавливаем RandomForest если отсутствует
            if 'rf' not in self.ensemble_model.models or self.ensemble_model.models['rf'] is None:
                print("🔧 Восстанавливаем модель RandomForest...")
                params = getattr(self.ensemble_model, 'adaptive_hyperparams', {})
                self.ensemble_model.models['rf'] = RandomForestClassifier(
                    n_estimators=params.get('rf_n_estimators', 100),
                    max_depth=params.get('rf_max_depth', 5),
                    min_samples_leaf=params.get('rf_min_samples_leaf', 3),
                    random_state=42,
                    class_weight='balanced'
                )

            # Восстанавливаем LogisticRegression если отсутствует
            if 'logreg' not in self.ensemble_model.models or self.ensemble_model.models['logreg'] is None:
                print("🔧 Восстанавливаем модель LogisticRegression...")
                params = getattr(self.ensemble_model, 'adaptive_hyperparams', {})
                self.ensemble_model.models['logreg'] = LogisticRegression(
                    random_state=42,
                    class_weight='balanced',
                    max_iter=1000,
                    C=params.get('logreg_C', 1.0),
                    penalty='l2'
                )

            # Восстанавливаем инкрементальные модели если отсутствуют
            if not hasattr(self.ensemble_model, 'incremental_models') or self.ensemble_model.incremental_models is None:
                self.ensemble_model.incremental_models = {}

            if 'sgd_incremental' not in self.ensemble_model.incremental_models:
                self.ensemble_model.incremental_models['sgd_incremental'] = SGDClassifier(
                    random_state=42,
                    loss='log_loss',
                    learning_rate='adaptive',
                    eta0=0.01
                )

            if 'passive_aggressive' not in self.ensemble_model.incremental_models:
                self.ensemble_model.incremental_models['passive_aggressive'] = PassiveAggressiveClassifier(
                    random_state=42,
                    C=1.0
                )

            print("✅ Отсутствующие модели восстановлены")

        except Exception as e:
            print(f"❌ Ошибка восстановления моделей: {e}")

    def save_state(self, filepath: Optional[str] = None):
        """Расширенное сохранение состояния индикатора"""
        if filepath is None:
            models_dir = self.config.get('models_dir', 'ai_models')
            os.makedirs(models_dir, exist_ok=True)
            filepath = os.path.join(models_dir, self.config.get('state_file', 'ai_indicator_state.json'))
        
        state = {
            'config': self.config,
            'bayesian_model': {
                'prior_bullish': self.bayesian_model.prior_bullish,
                'prior_bearish': self.bayesian_model.prior_bearish,
                'model_accuracy': self.bayesian_model.model_accuracy,
                'training_samples': self.bayesian_model.training_samples,
                'successful_predictions': self.bayesian_model.successful_predictions,
                'total_predictions': self.bayesian_model.total_predictions,
                'adaptation_rate': self.bayesian_model.adaptation_rate,
                'regularization_factor': self.bayesian_model.regularization_factor,
                'confidence_threshold': self.bayesian_model.confidence_threshold
            },
            'performance_stats': self.performance_stats,
            'ml_training_info': {
                'features_count': len(self.ml_features_history),
                'labels_count': len(self.ml_labels_history),
                'is_ml_trained': self.ensemble_model.is_trained,
                'last_training_timestamp': datetime.now().isoformat() if self.ensemble_model.is_trained else None,
                'cross_val_scores': self.ensemble_model.cross_val_scores if hasattr(self.ensemble_model, 'cross_val_scores') else []
            },
            'signals_history_stats': {
                'total_signals': len(self.ai_signals_history),
                'valid_signals': len([s for s in self.ai_signals_history if s.is_valid]),
                'bullish_signals': len([s for s in self.ai_signals_history if s.signal_type == 'bullish_reversal']),
                'bearish_signals': len([s for s in self.ai_signals_history if s.signal_type == 'bearish_reversal'])
            }
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            print(f"✅ Состояние индикатора сохранено в: {filepath}")
            
            # Сохраняем ML модели если они обучены
            if self.ensemble_model.is_trained:
                self.save_ml_models()
                
        except Exception as e:
            print(f"❌ Ошибка сохранения состояния: {e}")
    
    def load_state(self, filepath: Optional[str] = None):
        """Расширенная загрузка состояния индикатора"""
        if filepath is None:
            models_dir = self.config.get('models_dir', 'ai_models')
            filepath = os.path.join(models_dir, self.config.get('state_file', 'ai_indicator_state.json'))
        
        if not os.path.exists(filepath):
            print(f"⚠️  Файл состояния не найден: {filepath}")
            return False
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # Загрузка конфигурации
            self.config.update(state.get('config', {}))
            
            # Загрузка байесовской модели
            bayesian_data = state.get('bayesian_model', {})
            self.bayesian_model.prior_bullish = bayesian_data.get('prior_bullish', 0.6)
            self.bayesian_model.prior_bearish = bayesian_data.get('prior_bearish', 0.4)
            self.bayesian_model.model_accuracy = bayesian_data.get('model_accuracy', 0.5)
            self.bayesian_model.training_samples = bayesian_data.get('training_samples', 0)
            self.bayesian_model.successful_predictions = bayesian_data.get('successful_predictions', 0)
            self.bayesian_model.total_predictions = bayesian_data.get('total_predictions', 0)
            self.bayesian_model.adaptation_rate = bayesian_data.get('adaptation_rate', 0.05)
            self.bayesian_model.regularization_factor = bayesian_data.get('regularization_factor', 0.1)
            self.bayesian_model.confidence_threshold = bayesian_data.get('confidence_threshold', 0.7)
            
            # Загрузка статистики производительности
            self.performance_stats.update(state.get('performance_stats', {}))
            
            print(f"✅ Состояние индикатора загружено из: {filepath}")
            
            # Вывод информации о загруженном состоянии
            ml_info = state.get('ml_training_info', {})
            if ml_info:
                print(f"   ML обучено: {'Да' if ml_info.get('is_ml_trained', False) else 'Нет'}")
                print(f"   Обучающих образцов: {ml_info.get('features_count', 0)}")
                if ml_info.get('last_training_timestamp'):
                    print(f"   Последнее обучение: {ml_info.get('last_training_timestamp')}")
            
            signals_stats = state.get('signals_history_stats', {})
            if signals_stats:
                print(f"   Всего сигналов: {signals_stats.get('total_signals', 0)}")
                print(f"   Валидных сигналов: {signals_stats.get('valid_signals', 0)}")
            
            # Загрузка ML моделей если они были обучены
            if ml_info.get('is_ml_trained', False):
                if self.load_ml_models():
                    print("✅ ML модели успешно загружены")
                else:
                    print("⚠️  Не удалось загрузить ML модели")
            
            return True
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"❌ Ошибка загрузки состояния: {e}")
            return False
    
    def export_training_history(self, filepath: Optional[str] = None):
        """Экспорт истории обучения в CSV формат"""
        if filepath is None:
            models_dir = self.config.get('models_dir', 'ai_models')
            os.makedirs(models_dir, exist_ok=True)
            filepath = os.path.join(models_dir, self.config.get('training_history_file', 'training_history.csv'))
        
        try:
            # Подготовка данных для экспорта
            data_rows = []
            
            # Экспорт истории сигналов
            for i, signal in enumerate(self.ai_signals_history):
                row = {
                    'index': i,
                    'timestamp': signal.timestamp,
                    'signal_type': signal.signal_type,
                    'probability': signal.probability,
                    'confidence': signal.confidence,
                    'strength': signal.strength,
                    'math_expectation': signal.math_expectation,
                    'risk_score': signal.risk_score,
                    'is_valid': signal.is_valid,
                    'reasoning': signal.reasoning
                }
                data_rows.append(row)
            
            # Создание DataFrame и сохранение в CSV
            df = pd.DataFrame(data_rows)
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            print(f"✅ История обучения экспортирована в: {filepath}")
            print(f"   Экспортировано сигналов: {len(data_rows)}")
            
            # Дополнительный файл со статистикой
            stats_filepath = filepath.replace('.csv', '_stats.json')
            stats = {
                'export_timestamp': datetime.now().isoformat(),
                'total_signals': len(self.ai_signals_history),
                'valid_signals': len([s for s in self.ai_signals_history if s.is_valid]),
                'bayesian_model_stats': {
                    'accuracy': self.bayesian_model.model_accuracy,
                    'training_samples': self.bayesian_model.training_samples,
                    'prior_bullish': self.bayesian_model.prior_bullish,
                    'prior_bearish': self.bayesian_model.prior_bearish
                },
                'ml_model_stats': {
                    'is_trained': self.ensemble_model.is_trained,
                    'training_samples': len(self.ml_features_history),
                    'cross_val_scores': self.ensemble_model.cross_val_scores if hasattr(self.ensemble_model, 'cross_val_scores') else []
                },
                'performance_stats': self.performance_stats
            }
            
            with open(stats_filepath, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Статистика сохранена в: {stats_filepath}")
            
        except Exception as e:
            print(f"❌ Ошибка экспорта истории обучения: {e}")
    
    def _check_auto_save(self, bar_index: int):
        """Проверка необходимости автосохранения"""
        if not self.config.get('auto_save_enabled', True):
            return
        
        auto_save_interval = self.config.get('auto_save_interval', 100)
        
        # Сохраняем каждые N баров
        if bar_index > 0 and bar_index % auto_save_interval == 0:
            print(f"🔄 Автосохранение на баре {bar_index}...")
            self.save_state()
            self.export_training_history()
    
    def get_saved_models_info(self) -> Dict[str, Any]:
        """Получение информации о сохраненных моделях и данных"""
        models_dir = self.config.get('models_dir', 'ai_models')
        info = {
            'models_directory': os.path.abspath(models_dir),
            'files': {},
            'total_size_mb': 0
        }
        
        if not os.path.exists(models_dir):
            info['status'] = 'Директория не существует'
            return info
        
        # Список файлов для проверки
        files_to_check = [
            self.config.get('state_file', 'ai_indicator_state.json'),
            self.config.get('ml_models_file', 'ml_ensemble_models.pkl'),
            self.config.get('training_history_file', 'training_history.csv'),
            self.config.get('training_history_file', 'training_history.csv').replace('.csv', '_stats.json')
        ]
        
        total_size = 0
        for filename in files_to_check:
            filepath = os.path.join(models_dir, filename)
            if os.path.exists(filepath):
                file_stat = os.stat(filepath)
                file_size_mb = file_stat.st_size / (1024 * 1024)
                total_size += file_size_mb
                
                info['files'][filename] = {
                    'exists': True,
                    'size_mb': round(file_size_mb, 2),
                    'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                    'path': filepath
                }
            else:
                info['files'][filename] = {
                    'exists': False,
                    'path': filepath
                }
        
        info['total_size_mb'] = round(total_size, 2)
        info['status'] = 'OK'
        
        return info
    
    def cleanup_old_models(self, keep_last_n: int = 5):
        """Очистка старых сохраненных моделей, оставляя только последние N"""
        models_dir = self.config.get('models_dir', 'ai_models')
        
        if not os.path.exists(models_dir):
            print("⚠️  Директория моделей не существует")
            return
        
        # Получаем список всех pkl файлов с временными метками
        pkl_files = []
        for filename in os.listdir(models_dir):
            if filename.endswith('.pkl'):
                filepath = os.path.join(models_dir, filename)
                mtime = os.path.getmtime(filepath)
                pkl_files.append((filepath, mtime))
        
        # Сортируем по времени модификации
        pkl_files.sort(key=lambda x: x[1], reverse=True)
        
        # Удаляем старые файлы
        deleted_count = 0
        for filepath, _ in pkl_files[keep_last_n:]:
            try:
                os.remove(filepath)
                deleted_count += 1
                print(f"🗑️  Удален старый файл: {os.path.basename(filepath)}")
            except Exception as e:
                print(f"❌ Ошибка удаления файла {filepath}: {e}")
        
        if deleted_count > 0:
            print(f"✅ Удалено старых файлов: {deleted_count}")
        else:
            print("ℹ️  Нет файлов для удаления")