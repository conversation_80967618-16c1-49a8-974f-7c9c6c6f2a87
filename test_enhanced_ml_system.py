#!/usr/bin/env python3
"""
Тест улучшенной системы дообучения ML модели
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from datetime import datetime
from crypto_data_loader import CryptoDataLoader
from ai_indicator_full import AITrendReversalIndicator

def load_test_data():
    """Загрузка тестовых данных"""
    cache_files = [
        "training_data_cache/BTCUSDT_1h_90days.pkl",
        "training_data_cache/BTCUSDT_1h_LARGE_180days.pkl",
        "training_data_cache/BTCUSDT_1h_LARGE_365days.pkl"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            print(f"📂 Загружаем данные из {cache_file}")
            try:
                import pickle
                with open(cache_file, 'rb') as f:
                    df = pickle.load(f)
                print(f"✅ Загружено {len(df)} свечей")
                return df
            except Exception as e:
                print(f"❌ Ошибка загрузки: {e}")
    
    # Fallback: загружаем через API
    print("📥 Загружаем данные через API...")
    loader = CryptoDataLoader()
    df = loader.get_historical_data("BTCUSDT", "1h", days=30)
    return df

def test_enhanced_ml_system():
    """Тест улучшенной системы ML"""
    print("🚀 Тестирование улучшенной системы дообучения ML модели\n")
    
    # Загружаем данные
    df = load_test_data()
    if df.empty:
        print("❌ Не удалось загрузить данные")
        return False
    
    # Подготавливаем данные
    loader = CryptoDataLoader()
    ohlc_data, volumes = loader.prepare_data_for_indicator(df)
    
    # Создаем индикатор с улучшенными настройками
    config = {
        'enable_ai_learning': True,
        'models_dir': 'test_enhanced_models',
        'auto_save_enabled': True,
        'auto_save_interval': 25,  # Частое сохранение для теста
    }
    
    indicator = AITrendReversalIndicator(config)
    
    print("🤖 Тестирование улучшенной системы обучения...")
    
    # Фаза 1: Начальное обучение
    print("\n📚 Фаза 1: Начальное обучение модели")
    signals_generated = 0
    valid_signals = []
    
    min_window = 200
    for i in range(min_window, min(len(ohlc_data['close']), min_window + 100)):
        current_ohlc = {
            'open': ohlc_data['open'][:i+1],
            'high': ohlc_data['high'][:i+1],
            'low': ohlc_data['low'][:i+1],
            'close': ohlc_data['close'][:i+1]
        }
        current_volumes = volumes[:i+1]
        
        signal = indicator.process_bar(current_ohlc, current_volumes, i)
        
        if signal.is_valid:
            signals_generated += 1
            valid_signals.append((i, signal))
            print(f"   Сигнал #{signals_generated}: {signal.signal_type} (вероятность: {signal.probability:.1f}%)")
        
        # Проверяем инкрементальное обучение
        if i % 10 == 0 and len(indicator.ml_features_history) >= 30:
            print(f"   🔄 Инкрементальное обучение на баре {i}")
    
    print(f"✅ Фаза 1 завершена. Сгенерировано {signals_generated} валидных сигналов")
    
    # Фаза 2: Тестирование обратной связи
    print("\n🔄 Фаза 2: Тестирование системы обратной связи")
    
    feedback_count = 0
    for i, (bar_index, signal) in enumerate(valid_signals[:5]):  # Тестируем первые 5 сигналов
        # Симулируем результат сигнала
        future_bars = 10
        if bar_index + future_bars < len(ohlc_data['close']):
            signal_price = ohlc_data['close'][bar_index]
            future_price = ohlc_data['close'][bar_index + future_bars]
            
            # Определяем успешность сигнала
            if signal.signal_type == "bullish_reversal":
                success = future_price > signal_price * 1.02  # 2% рост
                return_pct = (future_price - signal_price) / signal_price * 100
            elif signal.signal_type == "bearish_reversal":
                success = future_price < signal_price * 0.98  # 2% падение
                return_pct = (signal_price - future_price) / signal_price * 100
            else:
                success = abs(future_price - signal_price) / signal_price < 0.01  # Стабильность
                return_pct = 0
            
            # Добавляем обратную связь
            indicator.add_signal_feedback(
                signal_timestamp=signal.timestamp,
                actual_outcome=success,
                return_pct=return_pct,
                notes=f"Тестовая обратная связь #{i+1}"
            )
            
            feedback_count += 1
            print(f"   Обратная связь #{feedback_count}: {signal.signal_type} -> {'✓' if success else '✗'} ({return_pct:+.1f}%)")
    
    print(f"✅ Фаза 2 завершена. Добавлено {feedback_count} записей обратной связи")
    
    # Фаза 3: Тестирование версионирования
    print("\n💾 Фаза 3: Тестирование версионирования моделей")
    
    # Сохраняем текущее состояние
    indicator.save_state()
    print("   Состояние модели сохранено")
    
    # Проверяем информацию о сохраненных моделях
    models_info = indicator.get_saved_models_info()
    print(f"   Директория моделей: {models_info['models_directory']}")
    print(f"   Общий размер: {models_info['total_size_mb']:.2f} MB")
    
    for filename, info in models_info['files'].items():
        if info['exists']:
            print(f"   📄 {filename}: {info['size_mb']:.2f} MB")
    
    # Фаза 4: Тестирование улучшенного переобучения
    print("\n🎯 Фаза 4: Тестирование улучшенного переобучения")
    
    # Принудительно запускаем улучшенное переобучение
    if len(indicator.ml_features_history) >= 50:
        print("   Запуск улучшенного переобучения...")
        indicator.train_ensemble_model_enhanced(
            list(indicator.ml_features_history), 
            list(indicator.ml_labels_history)
        )
        print("   ✅ Улучшенное переобучение завершено")
    
    # Проверяем статистику производительности
    print("\n📊 Статистика производительности:")
    print(f"   Общее количество сигналов: {indicator.performance_stats['total_signals']}")
    print(f"   Успешные сигналы: {indicator.performance_stats['successful_signals']}")
    print(f"   Винрейт: {indicator.performance_stats['win_rate']:.1%}")
    print(f"   Байесовская точность: {indicator.bayesian_model.model_accuracy:.1%}")
    print(f"   Обучающих образцов: {indicator.bayesian_model.training_samples}")
    
    # Проверяем новые поля
    if hasattr(indicator.ensemble_model, 'performance_history'):
        print(f"   История производительности: {len(indicator.ensemble_model.performance_history)} записей")
    if hasattr(indicator.ensemble_model, 'feedback_buffer'):
        print(f"   Буфер обратной связи: {len(indicator.ensemble_model.feedback_buffer)} записей")
    
    print("\n🎉 Тест улучшенной системы дообучения завершен успешно!")
    return True

def cleanup_test_files():
    """Очистка тестовых файлов"""
    import shutil
    test_dir = 'test_enhanced_models'
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"🧹 Тестовые файлы очищены: {test_dir}")

if __name__ == "__main__":
    print("🚀 Запуск теста улучшенной системы дообучения ML модели\n")
    
    # Создаем директорию для тестовых моделей
    os.makedirs('test_enhanced_models', exist_ok=True)
    
    try:
        success = test_enhanced_ml_system()
        
        if success:
            print("\n✅ Все тесты прошли успешно!")
            print("🎯 Улучшенная система дообучения работает корректно")
        else:
            print("\n❌ Тесты не прошли")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Очистка тестовых файлов
        cleanup_test_files()
