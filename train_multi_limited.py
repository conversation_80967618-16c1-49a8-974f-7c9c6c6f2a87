#!/usr/bin/env python3
"""
Ограниченное обучение мультиактивной модели
Использует меньший объем данных для быстрого обучения
"""

import os
import pickle
import pandas as pd
from datetime import datetime
from ai_indicator_full import AITrendReversalIndicator

def load_limited_training_data(max_candles_per_symbol=1000):
    """Загрузка ограниченного объема данных для быстрого обучения"""
    
    print("🚀 ЗАГРУЗКА ОГРАНИЧЕННЫХ ДАННЫХ ДЛЯ ОБУЧЕНИЯ")
    print("=" * 60)
    
    cache_dir = "training_data_cache"
    if not os.path.exists(cache_dir):
        print("❌ Директория с данными не найдена")
        return None
    
    # Приоритетные файлы для обучения (меньшие по размеру)
    priority_files = [
        "BTCUSDT_1h_90days.pkl",
        "ETHUSDT_1h_90days.pkl", 
        "SUIUSDT_1h_90days.pkl",
        "SOLUSDT_1h_90days.pkl"
    ]
    
    all_datasets = []
    total_candles = 0
    
    for filename in priority_files:
        filepath = os.path.join(cache_dir, filename)
        
        if os.path.exists(filepath):
            try:
                with open(filepath, 'rb') as f:
                    df = pickle.load(f)
                
                if not df.empty:
                    # Ограничиваем количество свечей
                    if len(df) > max_candles_per_symbol:
                        df = df.tail(max_candles_per_symbol).copy()
                    
                    # Добавляем метаданные
                    symbol = filename.split('_')[0]
                    df['symbol'] = symbol
                    df['timeframe'] = '1h'
                    
                    all_datasets.append(df)
                    total_candles += len(df)
                    
                    print(f"   ✅ {symbol}: {len(df)} свечей")
                
            except Exception as e:
                print(f"   ❌ Ошибка загрузки {filename}: {e}")
    
    if not all_datasets:
        print("❌ Не найдено данных для обучения")
        return None
    
    # Объединяем все данные
    print(f"\n🔄 Объединение {len(all_datasets)} датасетов...")
    combined_df = pd.concat(all_datasets, ignore_index=True)
    
    # Сортируем по времени
    combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
    
    print(f"✅ Объединено {len(combined_df):,} свечей")
    print(f"📅 Период: {combined_df['timestamp'].min()} - {combined_df['timestamp'].max()}")
    
    # Статистика по символам
    print(f"\n📊 Распределение данных:")
    for symbol in combined_df['symbol'].unique():
        symbol_data = combined_df[combined_df['symbol'] == symbol]
        print(f"   {symbol}: {len(symbol_data):,} свечей")
    
    return combined_df

def train_limited_model(training_data, model_name="multi_limited"):
    """Обучение на ограниченных данных"""
    
    print(f"\n🤖 ОБУЧЕНИЕ ОГРАНИЧЕННОЙ МОДЕЛИ: {model_name}")
    print("=" * 60)
    
    # Создаем директорию для модели
    model_dir = f"{model_name}_models"
    os.makedirs(model_dir, exist_ok=True)
    
    # Инициализируем индикатор
    config = {
        'lookback_period': 200,
        'models_dir': model_dir,
        'enable_ml': True,
        'enable_incremental_learning': True
    }
    indicator = AITrendReversalIndicator(config)
    
    print(f"📊 Начинаем обучение на {len(training_data):,} свечах...")
    
    # Группируем данные по символам
    symbols = training_data['symbol'].unique()
    
    total_processed = 0
    total_signals = 0
    
    for i, symbol in enumerate(symbols):
        print(f"\n💰 [{i+1}/{len(symbols)}] Обучение на данных {symbol}")
        print("-" * 40)
        
        symbol_data = training_data[training_data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('timestamp').reset_index(drop=True)
        
        print(f"📈 Обрабатываем {len(symbol_data):,} свечей для {symbol}")
        
        # Подготавливаем данные в формате OHLC списков
        ohlc_data = {
            'open': symbol_data['open'].tolist(),
            'high': symbol_data['high'].tolist(),
            'low': symbol_data['low'].tolist(),
            'close': symbol_data['close'].tolist()
        }
        volume_data = symbol_data['volume'].tolist()
        
        try:
            print(f"🎯 Обучение на данных {symbol}...")
            signal = indicator.process_bar(ohlc_data, volume_data, total_processed)
            
            if signal and signal.signal_type != 'none' and signal.is_valid:
                total_signals += 1
                print(f"   ✅ Получен валидный сигнал: {signal.signal_type}")
                print(f"      Вероятность: {signal.probability:.1f}%")
                print(f"      Уверенность: {signal.confidence:.1f}%")
            else:
                print(f"   📊 Сигнал: {signal.signal_type if signal else 'none'} (не валидный)")
            
            total_processed += len(symbol_data)
            
            # Сохранение после каждого символа
            print(f"   💾 Сохранение состояния...")
            indicator.save_state()
            indicator.save_ml_models()
            
        except Exception as e:
            print(f"   ❌ Ошибка обработки {symbol}: {e}")
            import traceback
            traceback.print_exc()
    
    # Финальная статистика
    print(f"\n📊 ИТОГОВАЯ СТАТИСТИКА ОБУЧЕНИЯ:")
    print(f"   Обработано свечей: {total_processed:,}")
    print(f"   Всего сигналов: {total_signals:,}")
    
    # Статистика модели
    stats = indicator.get_performance_stats()
    print(f"   Байесовская точность: {stats.get('model_accuracy', 0):.1f}%")
    print(f"   Обучающих образцов: {stats.get('training_samples', 0)}")
    print(f"   Средняя вероятность: {stats.get('avg_probability', 0):.1f}%")
    print(f"   Средняя уверенность: {stats.get('avg_confidence', 0):.1f}%")
    
    print(f"\n📁 Модель сохранена в директории: {model_dir}/")
    
    return indicator, total_processed, total_signals

def quick_training_session():
    """Быстрая сессия обучения"""
    
    print("🎯 ЗАПУСК БЫСТРОГО ОБУЧЕНИЯ НА ОГРАНИЧЕННЫХ ДАННЫХ")
    print("=" * 70)
    
    # Загружаем ограниченные данные (по 1000 свечей на символ)
    training_data = load_limited_training_data(max_candles_per_symbol=1000)
    if training_data is None:
        print("❌ Не удалось загрузить данные для обучения")
        return False
    
    # Обучаем модель
    model_name = f"multi_limited_{datetime.now().strftime('%Y%m%d_%H%M')}"
    
    try:
        indicator, processed, signals = train_limited_model(training_data, model_name)
        
        print(f"\n🎉 ОБУЧЕНИЕ ЗАВЕРШЕНО УСПЕШНО!")
        print(f"   Модель: {model_name}")
        print(f"   Обработано: {processed:,} свечей")
        print(f"   Сигналов: {signals:,}")
        
        # Тест загрузки
        print(f"\n🔄 Тестирование загрузки модели...")
        test_indicator = AITrendReversalIndicator({
            'models_dir': f"{model_name}_models",
            'enable_ml': True
        })
        
        load_success = test_indicator.load_state()
        ml_load_success = test_indicator.load_ml_models()
        
        if load_success:
            print("✅ Состояние загружено успешно")
        if ml_load_success:
            print("✅ ML модели загружены успешно")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ОШИБКА ОБУЧЕНИЯ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Запускаем быстрое обучение
    success = quick_training_session()
    
    if success:
        print(f"\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print(f"💡 Теперь можно запустить полное обучение на всех данных")
    else:
        print(f"\n❌ ТЕСТЫ НЕ ПРОШЛИ")
