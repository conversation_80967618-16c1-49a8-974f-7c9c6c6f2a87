#!/usr/bin/env python3
"""
Полный тест ML модели с достаточным количеством данных
"""

import sys
import os
import numpy as np
import pandas as pd
from ai_indicator_full import AITrendReversalIndicator

def create_synthetic_data(n_bars=300):
    """Создание синтетических данных с трендами и разворотами"""
    np.random.seed(42)
    
    # Базовая цена
    base_price = 50000
    prices = [base_price]
    
    # Создаем данные с периодическими трендами и разворотами
    for i in range(n_bars - 1):
        # Создаем циклические тренды
        cycle_position = (i % 50) / 50.0  # 50-барный цикл
        
        # Синусоидальный тренд с шумом
        trend_component = np.sin(cycle_position * 2 * np.pi) * 0.01
        noise_component = np.random.normal(0, 0.005)
        
        # Иногда добавляем сильные движения (разворотные точки)
        if i % 25 == 0:  # Каждые 25 баров
            trend_component += np.random.choice([-0.03, 0.03])  # Сильное движение
        
        change = trend_component + noise_component
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1000))  # Минимальная цена
    
    # Создаем OHLC данные
    ohlc_data = {
        'open': [],
        'high': [],
        'low': [],
        'close': []
    }
    
    volumes = []
    
    for i, close_price in enumerate(prices):
        open_price = prices[i-1] if i > 0 else close_price
        
        # Генерируем high и low с реалистичными спредами
        spread = abs(close_price - open_price)
        high_price = max(open_price, close_price) + spread * np.random.uniform(0, 0.5)
        low_price = min(open_price, close_price) - spread * np.random.uniform(0, 0.5)
        
        ohlc_data['open'].append(open_price)
        ohlc_data['high'].append(high_price)
        ohlc_data['low'].append(low_price)
        ohlc_data['close'].append(close_price)
        
        # Генерируем объем с всплесками в разворотных точках
        base_volume = np.random.uniform(1000, 3000)
        if i % 25 == 0:  # Объемные всплески в разворотных точках
            base_volume *= np.random.uniform(2, 5)
        volumes.append(base_volume)
    
    return ohlc_data, volumes

def test_full_ml_training():
    """Полный тест обучения ML модели"""
    print("🧪 Полный тест обучения ML модели...")
    
    # Создаем достаточно данных для обучения
    print("📊 Создание расширенного синтетического датасета...")
    ohlc_data, volumes = create_synthetic_data(n_bars=300)
    
    print(f"✅ Создано {len(ohlc_data['close'])} синтетических свечей")
    
    # Создание индикатора с настройками для обучения
    config = {
        'enable_ai_learning': True,
        'models_dir': 'test_models_full',
        'auto_save_enabled': False,
        'auto_save_interval': 30,  # Частое переобучение для теста
    }
    
    indicator = AITrendReversalIndicator(config)
    
    print("\n🤖 Обработка данных и обучение модели...")
    
    valid_signals = []
    all_signals = []
    
    # Минимальное окно для анализа
    min_window = 50
    
    # Обрабатываем все данные
    for i in range(min_window, len(ohlc_data['close'])):
        if i % 50 == 0:
            progress = (i - min_window) / (len(ohlc_data['close']) - min_window) * 100
            print(f"   Прогресс: {progress:.1f}% ({i}/{len(ohlc_data['close'])})")
        
        # Подготовка данных для текущего окна
        window_ohlc = {
            'open': ohlc_data['open'][:i+1],
            'high': ohlc_data['high'][:i+1],
            'low': ohlc_data['low'][:i+1],
            'close': ohlc_data['close'][:i+1]
        }
        window_volumes = volumes[:i+1]
        
        # Анализ текущего бара
        signal = indicator.process_bar(window_ohlc, window_volumes, i)
        all_signals.append(signal)
        
        # Сохраняем валидные сигналы
        if signal.is_valid:
            valid_signals.append(signal)
            price = ohlc_data['close'][i]
            
            print(f"\n✅ Валидный сигнал на баре {i}:")
            print(f"   Тип: {signal.signal_type}")
            print(f"   Цена: ${price:,.2f}")
            print(f"   Вероятность: {signal.probability:.1f}%")
            print(f"   Уверенность: {signal.confidence:.1f}%")
    
    # Статистика обучения
    print(f"\n📊 РЕЗУЛЬТАТЫ ПОЛНОГО ТЕСТИРОВАНИЯ:")
    print(f"   Обработано баров: {len(all_signals)}")
    print(f"   Всего сигналов: {len([s for s in all_signals if s.signal_type != 'none'])}")
    print(f"   Валидных сигналов: {len(valid_signals)}")
    
    if valid_signals:
        bullish = len([s for s in valid_signals if s.signal_type == 'bullish_reversal'])
        bearish = len([s for s in valid_signals if s.signal_type == 'bearish_reversal'])
        
        print(f"   Бычьих разворотов: {bullish}")
        print(f"   Медвежьих разворотов: {bearish}")
        
        avg_prob = np.mean([s.probability for s in valid_signals])
        avg_conf = np.mean([s.confidence for s in valid_signals])
        
        print(f"\n📈 СРЕДНИЕ ПОКАЗАТЕЛИ:")
        print(f"   Вероятность: {avg_prob:.1f}%")
        print(f"   Уверенность: {avg_conf:.1f}%")
    
    # Статистика модели
    stats = indicator.get_performance_stats()
    print(f"\n🤖 СТАТИСТИКА МОДЕЛИ:")
    print(f"   Байесовская точность: {stats['model_accuracy']:.1f}%")
    print(f"   Обучающих образцов: {stats['training_samples']}")
    
    # Проверяем ML модель
    ml_success = False
    if indicator.ensemble_model.is_trained:
        print(f"   ML модель обучена: ✅")
        if hasattr(indicator.ensemble_model, 'cross_val_scores') and indicator.ensemble_model.cross_val_scores:
            cv_score = np.mean(indicator.ensemble_model.cross_val_scores)
            print(f"   ML точность (CV): {cv_score:.3f}")
            
            # Проверяем, что точность разумная (не NaN и не 0)
            if not np.isnan(cv_score) and cv_score > 0.1:
                ml_success = True
                print("   ✅ ML модель работает корректно")
            else:
                print(f"   ❌ ML модель дает подозрительную точность: {cv_score}")
        
        # Тестируем предсказание
        if len(indicator.ml_features_history) > 0:
            test_features = indicator.ml_features_history[-1]
            try:
                prediction = indicator.predict_ensemble(np.array([test_features]))
                confidence = indicator.get_ensemble_confidence(test_features)
                print(f"   Тестовое предсказание: {prediction[0]}")
                print(f"   Уверенность ансамбля: {confidence:.3f}")
                
                # Проверяем, что предсказание валидно
                if prediction[0] in [-1, 0, 1] and not np.isnan(confidence):
                    print("   ✅ Предсказание работает корректно")
                    ml_success = True
                else:
                    print(f"   ❌ Некорректное предсказание: {prediction[0]}, confidence: {confidence}")
                    
            except Exception as e:
                print(f"   ❌ Ошибка предсказания: {e}")
                ml_success = False
    else:
        print(f"   ML модель обучена: ❌")
        if len(indicator.ml_features_history) < 50:
            print(f"   Причина: Недостаточно данных ({len(indicator.ml_features_history)}/50)")
        else:
            print(f"   Причина: Ошибка обучения")
    
    return ml_success and len(valid_signals) > 0

if __name__ == "__main__":
    print("🚀 Запуск полного теста ML модели\n")
    
    # Создаем директорию для тестовых моделей
    os.makedirs('test_models_full', exist_ok=True)
    
    try:
        # Полный тест
        success = test_full_ml_training()
        
        if success:
            print("\n🎉 Полный тест прошел успешно!")
            print("✅ ML модель работает корректно с исправлениями")
        else:
            print("\n❌ Полный тест не прошел")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Очистка тестовых файлов
        import shutil
        if os.path.exists('test_models_full'):
            shutil.rmtree('test_models_full')
            print("\n🧹 Тестовые файлы очищены")
