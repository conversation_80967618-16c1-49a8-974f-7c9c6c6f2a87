#!/usr/bin/env python3
"""
Обучение AI индикатора на реальных данных BTCUSDT
"""

import sys
import os
import time
from datetime import datetime
import numpy as np
import pandas as pd
from crypto_data_loader import CryptoDataLoader
from ai_indicator_full import AITrendReversalIndicator

def train_on_binance_data():
    """Обучение индикатора на данных Binance"""
    print("🚀 Запуск обучения AI индикатора на реальных данных BTCUSDT\n")
    
    # Инициализация загрузчика данных
    loader = CryptoDataLoader()
    
    # Загрузка исторических данных (попытка загрузить большой датасет)
    print("📊 Поиск и загрузка исторических данных BTCUSDT...")
    
    # Сначала пытаемся найти большой кэшированный датасет
    cache_dir = "training_data_cache"
    large_cache_files = [
        f"{cache_dir}/BTCUSDT_1h_LARGE_365days.pkl",
        f"{cache_dir}/BTCUSDT_1h_LARGE_180days.pkl", 
        f"{cache_dir}/BTCUSDT_1h_LARGE_90days.pkl"
    ]
    
    df_1h = None
    
    # Ищем существующий большой датасет
    for cache_file in large_cache_files:
        if os.path.exists(cache_file):
            print(f"📂 Найден большой датасет: {cache_file}")
            try:
                import pickle
                with open(cache_file, 'rb') as f:
                    df_1h = pickle.load(f)
                print(f"✅ Загружен из кэша: {len(df_1h)} свечей")
                break
            except Exception as e:
                print(f"❌ Ошибка загрузки кэша: {e}")
    
    # Если большого датасета нет, загружаем стандартный или создаем большой
    if df_1h is None or df_1h.empty:
        print("📥 Большой датасет не найден. Выберите опцию:")
        print("1. Загрузить 365 дней (рекомендуется для ML)")
        print("2. Загрузить 90 дней (быстрая загрузка)")
        print("3. Загрузить 30 дней (минимум)")
        
        try:
            choice = input("Ваш выбор (1-3): ").strip()
            
            if choice == "1":
                days = 365
                print("🚀 Загрузка годового датасета...")
                df_1h = loader.load_large_dataset("BTCUSDT", "1h", days)
            elif choice == "2":
                days = 90
                print("🚀 Загрузка 3-месячного датасета...")
                df_1h = loader.load_large_dataset("BTCUSDT", "1h", days)
            elif choice == "3":
                days = 30
                print("🚀 Загрузка месячного датасета...")
                df_1h = loader.get_historical_data("BTCUSDT", "1h", days)
            else:
                print("❌ Неверный выбор, загружаем 90 дней по умолчанию")
                df_1h = loader.load_large_dataset("BTCUSDT", "1h", 90)
                
        except KeyboardInterrupt:
            print("\n❌ Отменено пользователем")
            return None, None
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            print("🔄 Пробуем загрузить минимальный датасет...")
            df_1h = loader.get_historical_data("BTCUSDT", "1h", days=30)
    
    if df_1h.empty:
        print("❌ Не удалось загрузить данные")
        return None
    
    print(f"✅ Загружено {len(df_1h)} часовых свечей")
    print(f"📅 Период: {df_1h['timestamp'].min()} - {df_1h['timestamp'].max()}")
    
    # Подготовка данных для индикатора
    ohlc_data, volumes = loader.prepare_data_for_indicator(df_1h)
    
    # Создание индикатора с настройками для обучения
    config = {
        'enable_ai_learning': True,
        'models_dir': 'btc_models',
        'auto_save_enabled': True,
        'auto_save_interval': 50,
    }
    
    indicator = AITrendReversalIndicator(config)
    
    # Попытка загрузить предыдущее состояние
    if indicator.load_state():
        print("✅ Загружено предыдущее состояние модели")
        print(f"   Байесовская точность: {indicator.bayesian_model.model_accuracy:.2%}")
        print(f"   Обучающих образцов: {indicator.bayesian_model.training_samples}")
    else:
        print("🆕 Начинаем обучение с нуля")
    
    # Обработка исторических данных
    print("\n🤖 Обработка исторических данных и обучение модели...")
    
    valid_signals = []
    all_signals = []
    
    # Минимальное окно для анализа
    min_window = 200
    
    # Обрабатываем данные с скользящим окном
    for i in range(min_window, len(ohlc_data['close'])):
        # Прогресс бар
        if i % 50 == 0:
            progress = (i - min_window) / (len(ohlc_data['close']) - min_window) * 100
            print(f"   Прогресс: {progress:.1f}% ({i}/{len(ohlc_data['close'])})")
        
        # Подготовка данных для текущего окна
        window_ohlc = {
            'open': ohlc_data['open'][:i+1],
            'high': ohlc_data['high'][:i+1],
            'low': ohlc_data['low'][:i+1],
            'close': ohlc_data['close'][:i+1]
        }
        window_volumes = volumes[:i+1]
        
        # Анализ текущего бара
        signal = indicator.process_bar(window_ohlc, window_volumes, i)
        all_signals.append(signal)
        
        # Сохраняем валидные сигналы
        if signal.is_valid:
            valid_signals.append(signal)
            timestamp = df_1h.iloc[i]['timestamp']
            price = df_1h.iloc[i]['close']
            
            print(f"\n✅ Валидный сигнал на {timestamp}:")
            print(f"   Тип: {signal.signal_type}")
            print(f"   Цена: ${price:,.2f}")
            print(f"   Вероятность: {signal.probability:.1f}%")
            print(f"   Уверенность: {signal.confidence:.1f}%")
            print(f"   Обоснование: {signal.reasoning}")
    
    # Статистика обучения
    print("\n📊 РЕЗУЛЬТАТЫ ОБУЧЕНИЯ:")
    print(f"   Обработано баров: {len(all_signals)}")
    print(f"   Всего сигналов: {len([s for s in all_signals if s.signal_type != 'none'])}")
    print(f"   Валидных сигналов: {len(valid_signals)}")
    
    if valid_signals:
        bullish = len([s for s in valid_signals if s.signal_type == 'bullish_reversal'])
        bearish = len([s for s in valid_signals if s.signal_type == 'bearish_reversal'])
        
        print(f"   Бычьих разворотов: {bullish}")
        print(f"   Медвежьих разворотов: {bearish}")
        
        avg_prob = np.mean([s.probability for s in valid_signals])
        avg_conf = np.mean([s.confidence for s in valid_signals])
        avg_me = np.mean([s.math_expectation for s in valid_signals])
        
        print(f"\n📈 СРЕДНИЕ ПОКАЗАТЕЛИ:")
        print(f"   Вероятность: {avg_prob:.1f}%")
        print(f"   Уверенность: {avg_conf:.1f}%")
        print(f"   Мат. ожидание: {avg_me:.3f}")
    
    # Статистика модели
    stats = indicator.get_performance_stats()
    print(f"\n🤖 СТАТИСТИКА МОДЕЛИ:")
    print(f"   Байесовская точность: {stats['model_accuracy']:.1f}%")
    print(f"   Обучающих образцов: {stats['training_samples']}")
    
    if indicator.ensemble_model.is_trained:
        print(f"   ML модель обучена: ✅")
        if hasattr(indicator.ensemble_model, 'cross_val_scores') and indicator.ensemble_model.cross_val_scores:
            print(f"   ML точность (CV): {np.mean(indicator.ensemble_model.cross_val_scores):.3f}")
    else:
        print(f"   ML модель обучена: ❌")
    
    # Сохранение финального состояния
    print("\n💾 Сохранение обученной модели...")
    indicator.save_state()
    indicator.export_training_history()
    
    # Информация о сохраненных файлах
    saved_info = indicator.get_saved_models_info()
    print(f"\n📁 Сохраненные файлы:")
    for filename, info in saved_info['files'].items():
        if info['exists']:
            print(f"   {filename}: {info['size_mb']:.2f} MB")
    
    return indicator, valid_signals

def realtime_monitoring(indicator: AITrendReversalIndicator):
    """Мониторинг в реальном времени с обученной моделью"""
    print("\n🔄 Запуск мониторинга в реальном времени...")
    
    loader = CryptoDataLoader()
    
    # Загружаем последние данные для контекста
    df_recent = loader.get_historical_data("BTCUSDT", "1h", days=2)
    ohlc_data, volumes = loader.prepare_data_for_indicator(df_recent)
    
    def process_realtime_update(price_data):
        """Обработка обновления цены в реальном времени"""
        # Добавляем новую свечу (упрощенно)
        current_price = price_data['price']
        
        # Создаем псевдо-свечу на основе текущей цены
        ohlc_data['open'].append(ohlc_data['close'][-1])
        ohlc_data['high'].append(max(current_price, ohlc_data['close'][-1]))
        ohlc_data['low'].append(min(current_price, ohlc_data['close'][-1]))
        ohlc_data['close'].append(current_price)
        volumes.append(price_data.get('volume', volumes[-1]))
        
        # Анализируем с индикатором
        signal = indicator.process_bar(ohlc_data, volumes, len(ohlc_data['close']))
        
        # Выводим информацию
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n[{timestamp}] BTC: ${current_price:,.2f} | Изменение 24ч: {price_data['change_24h']:.2f}%")
        
        if signal.signal_type != "none":
            print(f"📊 Сигнал: {signal.signal_type} | Вероятность: {signal.probability:.1f}%")
            
            if signal.is_valid:
                print(f"⚠️  ВАЛИДНЫЙ СИГНАЛ РАЗВОРОТА!")
                print(f"   Уверенность: {signal.confidence:.1f}%")
                print(f"   Сила: {signal.strength}")
                print(f"   Мат. ожидание: {signal.math_expectation:.3f}")
                print(f"   Обоснование: {signal.reasoning}")
        
        # Удаляем последнюю добавленную свечу для следующей итерации
        for key in ohlc_data:
            ohlc_data[key].pop()
        volumes.pop()
    
    # Запуск стриминга
    try:
        loader.stream_realtime_updates("BTCUSDT", callback=process_realtime_update, interval=10)
    except KeyboardInterrupt:
        print("\n⏹️  Мониторинг остановлен")

def backtest_signals(indicator: AITrendReversalIndicator, df: pd.DataFrame, 
                    valid_signals: list, lookforward: int = 10):
    """Бэктест сигналов на исторических данных"""
    print("\n📊 Бэктестирование сигналов...")
    
    successful_signals = 0
    total_return = 0.0
    
    for signal in valid_signals:
        signal_idx = signal.timestamp
        
        if signal_idx + lookforward < len(df):
            entry_price = df.iloc[signal_idx]['close']
            
            # Проверяем результат через lookforward свечей
            future_prices = df.iloc[signal_idx:signal_idx+lookforward+1]['close'].values
            
            if signal.signal_type == "bullish_reversal":
                # Для бычьего сигнала проверяем максимальную цену
                max_price = np.max(future_prices[1:])
                return_pct = (max_price - entry_price) / entry_price * 100
                
                if return_pct >= 2.0:  # Минимум 2% прибыли
                    successful_signals += 1
                    total_return += return_pct
                    
            elif signal.signal_type == "bearish_reversal":
                # Для медвежьего сигнала проверяем минимальную цену
                min_price = np.min(future_prices[1:])
                return_pct = (entry_price - min_price) / entry_price * 100
                
                if return_pct >= 2.0:  # Минимум 2% прибыли
                    successful_signals += 1
                    total_return += return_pct
    
    if valid_signals:
        success_rate = successful_signals / len(valid_signals) * 100
        avg_return = total_return / len(valid_signals) if valid_signals else 0
        
        print(f"✅ Результаты бэктеста:")
        print(f"   Успешных сигналов: {successful_signals}/{len(valid_signals)} ({success_rate:.1f}%)")
        print(f"   Средняя доходность: {avg_return:.2f}%")
        
        # Обновляем байесовскую модель результатами
        for i, signal in enumerate(valid_signals):
            if signal.timestamp + lookforward < len(df):
                outcome = i < successful_signals  # Упрощенная логика
                indicator.update_bayesian_model(signal.signal_type, outcome)
        
        print(f"   Байесовская модель обновлена результатами бэктеста")

if __name__ == "__main__":
    # Обучение на исторических данных
    indicator, valid_signals = train_on_binance_data()
    
    if indicator and valid_signals:
        # Опциональный бэктест
        loader = CryptoDataLoader()
        df = loader.get_historical_data("BTCUSDT", "1h", days=30)
        backtest_signals(indicator, df, valid_signals)
        
        # Запрос на мониторинг в реальном времени
        print("\n❓ Запустить мониторинг в реальном времени? (y/n): ", end='')
        if input().lower() == 'y':
            realtime_monitoring(indicator)
    else:
        print("\n❌ Обучение не удалось")