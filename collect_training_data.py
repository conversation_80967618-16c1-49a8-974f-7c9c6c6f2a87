#!/usr/bin/env python3
"""
Скрипт для сбора обучающих данных по криптовалютам
Собирает данные для BTCUSDT, ETHUSDT, SUIUSDT, SOLUSDT
Включая малые таймфреймы (1m, 5m) для детального анализа
"""

import os
import time
from datetime import datetime
from crypto_data_loader import CryptoDataLoader
import pandas as pd

def collect_comprehensive_data():
    """Сбор комплексных данных для обучения ML модели"""
    
    print("🚀 ЗАПУСК СБОРА ОБУЧАЮЩИХ ДАННЫХ")
    print("=" * 60)
    
    # Инициализация загрузчика
    loader = CryptoDataLoader(cache_dir="training_data_cache")
    
    # Конфигурация сбора данных
    symbols = ["BTCUSDT", "ETHUSDT", "SUIUSDT", "SOLUSDT"]
    
    # Таймфреймы с разными периодами для оптимизации
    timeframes_config = {
        "1m": [7, 30],      # 1 минута: 7 и 30 дней (много данных)
        "5m": [14, 60],     # 5 минут: 14 и 60 дней
        "15m": [30, 90],    # 15 минут: 30 и 90 дней
        "1h": [90, 365],    # 1 час: 90 и 365 дней
        "4h": [180, 730],   # 4 часа: 180 и 730 дней
        "1d": [365, 1825]   # 1 день: 1 и 5 лет
    }
    
    total_tasks = sum(len(periods) for periods in timeframes_config.values()) * len(symbols)
    current_task = 0
    
    print(f"📊 Планируется загрузить данные для:")
    print(f"   Символы: {', '.join(symbols)}")
    print(f"   Таймфреймы: {', '.join(timeframes_config.keys())}")
    print(f"   Общее количество задач: {total_tasks}")
    print()
    
    # Статистика сбора
    success_count = 0
    failed_count = 0
    total_candles = 0
    
    # Сбор данных для каждого символа и таймфрейма
    for symbol in symbols:
        print(f"\n💰 ОБРАБОТКА СИМВОЛА: {symbol}")
        print("-" * 40)
        
        for timeframe, periods in timeframes_config.items():
            print(f"\n⏰ Таймфрейм: {timeframe}")
            
            for days in periods:
                current_task += 1
                progress = (current_task / total_tasks) * 100
                
                print(f"\n📥 [{current_task}/{total_tasks}] ({progress:.1f}%) "
                      f"Загрузка {symbol} {timeframe} за {days} дней...")
                
                try:
                    # Проверяем, есть ли уже данные в кэше
                    cache_file = f"training_data_cache/{symbol}_{timeframe}_{days}days.pkl"
                    
                    if os.path.exists(cache_file):
                        # Проверяем возраст кэша
                        cache_age = time.time() - os.path.getmtime(cache_file)
                        cache_hours = cache_age / 3600
                        
                        if cache_hours < 24:  # Кэш свежий
                            print(f"   ✅ Данные уже есть в кэше (возраст: {cache_hours:.1f}ч)")
                            
                            # Загружаем для подсчета статистики
                            import pickle
                            with open(cache_file, 'rb') as f:
                                df = pickle.load(f)
                                total_candles += len(df)
                                success_count += 1
                            continue
                    
                    # Загружаем данные
                    if days >= 365:
                        # Для больших периодов используем специальный метод
                        df = loader.load_large_dataset(symbol, timeframe, days)
                    else:
                        # Для обычных периодов
                        df = loader.get_historical_data(symbol, timeframe, days)
                    
                    if not df.empty:
                        print(f"   ✅ Успешно: {len(df)} свечей")
                        print(f"   📅 Период: {df['timestamp'].min()} - {df['timestamp'].max()}")
                        total_candles += len(df)
                        success_count += 1
                    else:
                        print(f"   ❌ Не удалось загрузить данные")
                        failed_count += 1
                    
                except Exception as e:
                    print(f"   ❌ Ошибка: {e}")
                    failed_count += 1
                
                # Пауза между запросами для избежания rate limit
                if timeframe in ["1m", "5m"]:
                    time.sleep(2.0)  # Больше пауза для мелких таймфреймов
                else:
                    time.sleep(1.0)
        
        print(f"\n✅ Завершена обработка {symbol}")
        print(f"   Успешно: {success_count}")
        print(f"   Ошибок: {failed_count}")
        
        # Большая пауза между символами
        if symbol != symbols[-1]:  # Не делаем паузу после последнего символа
            print("   ⏳ Пауза 5 секунд перед следующим символом...")
            time.sleep(5.0)
    
    # Итоговая статистика
    print("\n" + "=" * 60)
    print("📊 ИТОГОВАЯ СТАТИСТИКА СБОРА ДАННЫХ")
    print("=" * 60)
    print(f"✅ Успешных загрузок: {success_count}")
    print(f"❌ Неудачных загрузок: {failed_count}")
    print(f"📈 Общее количество свечей: {total_candles:,}")
    print(f"🎯 Процент успеха: {(success_count/(success_count+failed_count)*100):.1f}%")
    
    # Анализ размера данных
    cache_dir = "training_data_cache"
    if os.path.exists(cache_dir):
        total_size = 0
        file_count = 0
        
        for filename in os.listdir(cache_dir):
            if filename.endswith('.pkl'):
                filepath = os.path.join(cache_dir, filename)
                total_size += os.path.getsize(filepath)
                file_count += 1
        
        total_size_mb = total_size / (1024 * 1024)
        print(f"💾 Размер кэша: {total_size_mb:.1f} MB ({file_count} файлов)")
    
    print("\n🎉 СБОР ДАННЫХ ЗАВЕРШЕН!")
    
    return success_count, failed_count, total_candles

def analyze_collected_data():
    """Анализ собранных данных"""
    
    print("\n🔍 АНАЛИЗ СОБРАННЫХ ДАННЫХ")
    print("=" * 50)
    
    cache_dir = "training_data_cache"
    if not os.path.exists(cache_dir):
        print("❌ Директория с данными не найдена")
        return
    
    import pickle
    
    symbols = ["BTCUSDT", "ETHUSDT", "SUIUSDT", "SOLUSDT"]
    timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
    
    print(f"{'Символ':<10} {'Таймфрейм':<10} {'Период':<15} {'Свечей':<10} {'Размер':<10}")
    print("-" * 65)
    
    total_files = 0
    total_candles = 0
    
    for filename in sorted(os.listdir(cache_dir)):
        if filename.endswith('.pkl'):
            filepath = os.path.join(cache_dir, filename)
            
            try:
                with open(filepath, 'rb') as f:
                    df = pickle.load(f)
                
                # Парсим имя файла
                parts = filename.replace('.pkl', '').split('_')
                symbol = parts[0]
                timeframe = parts[1]
                period = '_'.join(parts[2:])
                
                file_size = os.path.getsize(filepath) / 1024  # KB
                
                print(f"{symbol:<10} {timeframe:<10} {period:<15} {len(df):<10} {file_size:.1f}KB")
                
                total_files += 1
                total_candles += len(df)
                
            except Exception as e:
                print(f"❌ Ошибка чтения {filename}: {e}")
    
    print("-" * 65)
    print(f"📊 Итого: {total_files} файлов, {total_candles:,} свечей")

def quick_data_check():
    """Быстрая проверка доступности данных"""
    
    print("🔍 БЫСТРАЯ ПРОВЕРКА ДОСТУПНОСТИ ДАННЫХ")
    print("=" * 50)
    
    loader = CryptoDataLoader(cache_dir="training_data_cache")
    
    # Тестируем один запрос для каждого символа
    test_symbols = ["BTCUSDT", "ETHUSDT", "SUIUSDT", "SOLUSDT"]
    
    for symbol in test_symbols:
        print(f"\n📊 Тестирование {symbol}...")
        
        try:
            # Пробуем загрузить небольшой объем данных
            df = loader.get_historical_data(symbol, "1h", days=1, force_reload=True)
            
            if not df.empty:
                last_price = df.iloc[-1]['close']
                print(f"   ✅ Доступен: последняя цена ${last_price:,.2f}")
            else:
                print(f"   ❌ Недоступен")
                
        except Exception as e:
            print(f"   ❌ Ошибка: {e}")
        
        time.sleep(0.5)  # Небольшая пауза

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "check":
            quick_data_check()
        elif command == "analyze":
            analyze_collected_data()
        else:
            print("❌ Неизвестная команда. Используйте: check, analyze")
    else:
        # Основной сбор данных
        collect_comprehensive_data()
        
        # Анализ после сбора
        print("\n" + "="*60)
        analyze_collected_data()
