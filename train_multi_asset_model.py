#!/usr/bin/env python3
"""
Скрипт для дообучения модели на всех собранных данных
Использует данные из training_data_cache для обучения мультиактивной модели
"""

import os
import sys
import time
import pickle
import pandas as pd
from datetime import datetime
from ai_indicator_full import AITrendReversalIndicator
from crypto_data_loader import CryptoDataLoader

def load_multi_asset_training_data():
    """Загрузка всех данных для мультиактивного обучения"""
    
    print("🚀 ЗАГРУЗКА МУЛЬТИАКТИВНЫХ ДАННЫХ ДЛЯ ОБУЧЕНИЯ")
    print("=" * 60)
    
    cache_dir = "training_data_cache"
    if not os.path.exists(cache_dir):
        print("❌ Директория с данными не найдена")
        return None
    
    # Конфигурация для обучения
    symbols = ["BTCUSDT", "ETHUSDT", "SUIUSDT", "SOLUSDT"]
    
    # Приоритетные таймфреймы для обучения (от самых важных к менее важным)
    timeframes_priority = [
        ("1h", ["90days", "LARGE_365days"]),     # Основной таймфрейм
        ("4h", ["180days", "LARGE_730days"]),    # Средний таймфрейм
        ("15m", ["30days", "90days"]),           # Краткосрочный
        ("1d", ["90days", "LARGE_365days"]),     # Долгосрочный
        ("5m", ["14days", "60days"]),            # Высокочастотный
        ("1m", ["7days"])                        # Сверхвысокочастотный (ограниченно)
    ]
    
    all_datasets = []
    total_candles = 0
    
    for symbol in symbols:
        print(f"\n💰 Загрузка данных для {symbol}:")
        symbol_candles = 0
        
        for timeframe, periods in timeframes_priority:
            for period in periods:
                filename = f"{symbol}_{timeframe}_{period}.pkl"
                filepath = os.path.join(cache_dir, filename)
                
                if os.path.exists(filepath):
                    try:
                        with open(filepath, 'rb') as f:
                            df = pickle.load(f)
                        
                        if not df.empty:
                            # Добавляем метаданные
                            df['symbol'] = symbol
                            df['timeframe'] = timeframe
                            df['period'] = period
                            
                            all_datasets.append(df)
                            symbol_candles += len(df)
                            
                            print(f"   ✅ {timeframe} {period}: {len(df)} свечей")
                        
                    except Exception as e:
                        print(f"   ❌ Ошибка загрузки {filename}: {e}")
        
        total_candles += symbol_candles
        print(f"   📊 Итого для {symbol}: {symbol_candles:,} свечей")
    
    if not all_datasets:
        print("❌ Не найдено данных для обучения")
        return None
    
    # Объединяем все данные
    print(f"\n🔄 Объединение {len(all_datasets)} датасетов...")
    combined_df = pd.concat(all_datasets, ignore_index=True)
    
    # Сортируем по времени для корректного обучения
    combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
    
    print(f"✅ Объединено {len(combined_df):,} свечей из {len(symbols)} активов")
    print(f"📅 Период: {combined_df['timestamp'].min()} - {combined_df['timestamp'].max()}")
    
    # Статистика по активам и таймфреймам
    print(f"\n📊 Распределение данных:")
    for symbol in symbols:
        symbol_data = combined_df[combined_df['symbol'] == symbol]
        print(f"   {symbol}: {len(symbol_data):,} свечей")
    
    print(f"\n⏰ Распределение по таймфреймам:")
    for timeframe in combined_df['timeframe'].unique():
        tf_data = combined_df[combined_df['timeframe'] == timeframe]
        print(f"   {timeframe}: {len(tf_data):,} свечей")
    
    return combined_df

def train_multi_asset_model(training_data, model_name="multi_asset"):
    """Обучение мультиактивной модели"""
    
    print(f"\n🤖 ОБУЧЕНИЕ МУЛЬТИАКТИВНОЙ МОДЕЛИ: {model_name}")
    print("=" * 60)
    
    # Создаем директорию для модели
    model_dir = f"{model_name}_models"
    os.makedirs(model_dir, exist_ok=True)
    
    # Инициализируем индикатор
    config = {
        'lookback_period': 200,
        'model_dir': model_dir,
        'enable_ml': True,
        'enable_incremental_learning': True
    }
    indicator = AITrendReversalIndicator(config)
    
    print(f"📊 Начинаем обучение на {len(training_data):,} свечах...")
    
    # Группируем данные по символам для последовательного обучения
    symbols = training_data['symbol'].unique()
    
    total_processed = 0
    total_signals = 0
    
    for i, symbol in enumerate(symbols):
        print(f"\n💰 [{i+1}/{len(symbols)}] Обучение на данных {symbol}")
        print("-" * 40)
        
        symbol_data = training_data[training_data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('timestamp').reset_index(drop=True)

        print(f"📈 Обрабатываем {len(symbol_data):,} свечей для {symbol}")

        # Инициализируем счетчик сигналов для символа
        symbol_signals = 0
        
        # Обучение последовательно - свеча за свечой с нарастающим окном
        print(f"📈 Последовательное обучение на {len(symbol_data)} свечах {symbol}...")

        # Подготавливаем данные в формате списков
        all_ohlc = {
            'open': symbol_data['open'].tolist(),
            'high': symbol_data['high'].tolist(),
            'low': symbol_data['low'].tolist(),
            'close': symbol_data['close'].tolist()
        }
        all_volume = symbol_data['volume'].tolist()

        # Обрабатываем каждую свечу с нарастающим окном (как в train_on_real_data.py)
        try:
            for i in range(len(all_ohlc['close'])):
                # Создаем нарастающее окно данных до текущей позиции
                window_ohlc = {
                    'open': all_ohlc['open'][:i+1],
                    'high': all_ohlc['high'][:i+1],
                    'low': all_ohlc['low'][:i+1],
                    'close': all_ohlc['close'][:i+1]
                }
                window_volumes = all_volume[:i+1]

                # Анализ текущего бара
                signal = indicator.process_bar(window_ohlc, window_volumes, total_processed + i)

                if signal and signal.signal_type != 'none' and signal.is_valid:
                    symbol_signals += 1
                    total_signals += 1

                # Показываем прогресс каждые 1000 свечей
                if (i + 1) % 1000 == 0:
                    progress = ((i + 1) / len(all_ohlc['close'])) * 100
                    print(f"   📊 Прогресс {symbol}: {progress:.1f}% ({i+1}/{len(all_ohlc['close'])}) | Сигналов: {symbol_signals}")

                # Периодическое сохранение каждые 5000 свечей
                if (total_processed + i + 1) % 5000 == 0:
                    print(f"   💾 Автосохранение на свече {total_processed + i + 1}...")
                    indicator.save_state()

            total_processed += len(symbol_data)

            print(f"   ✅ {symbol} завершен: {symbol_signals} сигналов из {len(symbol_data)} свечей ({(symbol_signals/len(symbol_data)*100):.3f}%)")

            # Сохранение после каждого символа
            print(f"   💾 Сохранение после обработки {symbol}...")
            indicator.save_state()
            indicator.save_ml_models()

        except Exception as e:
            print(f"   ❌ Ошибка обработки {symbol}: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"   ✅ {symbol} завершен: {symbol_signals} сигналов из {len(symbol_data)} свечей")
        
        # Сохранение после каждого символа
        indicator.save_state()
        
        # Небольшая пауза между символами
        time.sleep(1)
    
    # Финальное сохранение
    print(f"\n💾 Финальное сохранение модели...")
    indicator.save_state()
    
    # Статистика обучения
    print(f"\n📊 ИТОГОВАЯ СТАТИСТИКА ОБУЧЕНИЯ:")
    print(f"   Обработано свечей: {total_processed:,}")
    print(f"   Всего сигналов: {total_signals:,}")
    print(f"   Процент сигналов: {(total_signals/total_processed*100):.2f}%")
    
    # Статистика модели
    if hasattr(indicator, 'bayesian_model'):
        print(f"   Байесовская точность: {indicator.bayesian_model.model_accuracy*100:.1f}%")
        print(f"   Обучающих образцов: {indicator.bayesian_model.training_samples}")
    
    if hasattr(indicator, 'ensemble_model') and indicator.ensemble_model.is_trained:
        print(f"   ML модель обучена: ✅")
        print(f"   ML точность: {indicator.ensemble_model.last_cv_score:.3f}")
    
    print(f"\n📁 Модель сохранена в директории: {model_dir}/")
    
    return indicator, total_processed, total_signals

def incremental_training_session():
    """Сессия инкрементального обучения"""
    
    print("🎯 ЗАПУСК ИНКРЕМЕНТАЛЬНОГО ОБУЧЕНИЯ НА МУЛЬТИАКТИВНЫХ ДАННЫХ")
    print("=" * 70)
    
    # Загружаем данные
    training_data = load_multi_asset_training_data()
    if training_data is None:
        print("❌ Не удалось загрузить данные для обучения")
        return False
    
    # Обучаем модель
    model_name = f"multi_asset_{datetime.now().strftime('%Y%m%d_%H%M')}"
    
    try:
        indicator, processed, signals = train_multi_asset_model(training_data, model_name)
        
        print(f"\n🎉 ОБУЧЕНИЕ ЗАВЕРШЕНО УСПЕШНО!")
        print(f"   Модель: {model_name}")
        print(f"   Обработано: {processed:,} свечей")
        print(f"   Сигналов: {signals:,}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ОШИБКА ОБУЧЕНИЯ: {e}")
        import traceback
        traceback.print_exc()
        return False

def continue_existing_model_training(model_dir):
    """Продолжение обучения существующей модели"""
    
    print(f"🔄 ПРОДОЛЖЕНИЕ ОБУЧЕНИЯ МОДЕЛИ: {model_dir}")
    print("=" * 60)
    
    if not os.path.exists(model_dir):
        print(f"❌ Директория модели не найдена: {model_dir}")
        return False
    
    # Загружаем данные
    training_data = load_multi_asset_training_data()
    if training_data is None:
        return False
    
    # Инициализируем индикатор с существующей моделью
    config = {
        'lookback_period': 200,
        'model_dir': model_dir,
        'enable_ml': True,
        'enable_incremental_learning': True
    }
    indicator = AITrendReversalIndicator(config)
    
    # Загружаем состояние
    try:
        indicator.load_state()
        print(f"✅ Загружена существующая модель из {model_dir}")
    except Exception as e:
        print(f"⚠️  Не удалось загрузить состояние: {e}")
        print("   Начинаем обучение с нуля...")
    
    # Продолжаем обучение
    try:
        _, processed, signals = train_multi_asset_model(training_data, model_dir.replace('_models', ''))
        
        print(f"\n🎉 ДООБУЧЕНИЕ ЗАВЕРШЕНО!")
        print(f"   Дополнительно обработано: {processed:,} свечей")
        print(f"   Дополнительно сигналов: {signals:,}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ОШИБКА ДООБУЧЕНИЯ: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "continue" and len(sys.argv) > 2:
            model_dir = sys.argv[2]
            continue_existing_model_training(model_dir)
        elif command == "load":
            # Просто загружаем и показываем данные
            data = load_multi_asset_training_data()
            if data is not None:
                print(f"\n✅ Данные готовы к обучению: {len(data):,} свечей")
        else:
            print("❌ Неизвестная команда")
            print("Использование:")
            print("  python train_multi_asset_model.py                    # Новое обучение")
            print("  python train_multi_asset_model.py continue <dir>     # Продолжить обучение")
            print("  python train_multi_asset_model.py load               # Только загрузить данные")
    else:
        # Основное обучение
        incremental_training_session()
