#!/usr/bin/env python3
"""
Простой тест обучения мультиактивной модели
"""

import os
import pickle
import pandas as pd
from ai_indicator_full import AITrendReversalIndicator

def simple_training_test():
    """Простой тест обучения на небольшом объеме данных"""
    
    print("🧪 ПРОСТОЙ ТЕСТ ОБУЧЕНИЯ МУЛЬТИАКТИВНОЙ МОДЕЛИ")
    print("=" * 60)
    
    # Загружаем один небольшой файл для теста
    cache_dir = "training_data_cache"
    test_file = "BTCUSDT_1h_90days.pkl"
    test_path = os.path.join(cache_dir, test_file)
    
    if not os.path.exists(test_path):
        print(f"❌ Тестовый файл не найден: {test_path}")
        return False
    
    # Загружаем данные
    print(f"📂 Загрузка тестовых данных: {test_file}")
    with open(test_path, 'rb') as f:
        df = pickle.load(f)
    
    print(f"✅ Загружено {len(df)} свечей")
    print(f"📅 Период: {df['timestamp'].min()} - {df['timestamp'].max()}")
    
    # Создаем индикатор
    config = {
        'lookback_period': 200,
        'models_dir': 'test_models',
        'enable_ml': True,
        'enable_incremental_learning': True
    }
    
    print(f"\n🤖 Инициализация индикатора...")
    indicator = AITrendReversalIndicator(config)
    
    # Подготавливаем данные в правильном формате
    print(f"\n📊 Подготовка данных для обучения...")
    
    # Берем только первые 100 свечей для быстрого теста
    test_size = min(100, len(df))
    df_test = df.head(test_size).copy()
    
    # Преобразуем в формат списков OHLC
    ohlc_data = {
        'open': df_test['open'].tolist(),
        'high': df_test['high'].tolist(),
        'low': df_test['low'].tolist(),
        'close': df_test['close'].tolist()
    }
    volume_data = df_test['volume'].tolist()
    
    print(f"✅ Подготовлено {test_size} свечей для обучения")
    
    # Обучение
    print(f"\n🎯 Начинаем обучение...")
    
    try:
        # Обрабатываем данные
        signal = indicator.process_bar(ohlc_data, volume_data, 0)
        
        print(f"✅ Обучение завершено!")
        print(f"📊 Результат:")
        print(f"   Тип сигнала: {signal.signal_type}")
        print(f"   Вероятность: {signal.probability:.1f}%")
        print(f"   Уверенность: {signal.confidence:.1f}%")
        print(f"   Валидный: {signal.is_valid}")
        
        # Сохраняем состояние
        print(f"\n💾 Сохранение состояния...")
        indicator.save_state()
        
        # Сохраняем ML модели
        indicator.save_ml_models()
        
        # Статистика
        stats = indicator.get_performance_stats()
        print(f"\n📈 Статистика:")
        print(f"   Всего сигналов: {stats.get('total_signals', 0)}")
        print(f"   Средняя вероятность: {stats.get('avg_probability', 0):.1f}%")
        print(f"   Средняя уверенность: {stats.get('avg_confidence', 0):.1f}%")
        print(f"   Точность модели: {stats.get('model_accuracy', 0):.1f}%")
        print(f"   Обучающих образцов: {stats.get('training_samples', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка обучения: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """Тест загрузки сохраненной модели"""
    
    print("\n🔄 ТЕСТ ЗАГРУЗКИ МОДЕЛИ")
    print("=" * 40)
    
    config = {
        'lookback_period': 200,
        'models_dir': 'test_models',
        'enable_ml': True,
        'enable_incremental_learning': True
    }
    
    indicator = AITrendReversalIndicator(config)
    
    try:
        # Загружаем состояние
        success = indicator.load_state()
        if success:
            print("✅ Состояние загружено успешно")
        else:
            print("⚠️  Состояние не загружено")
        
        # Загружаем ML модели
        ml_success = indicator.load_ml_models()
        if ml_success:
            print("✅ ML модели загружены успешно")
        else:
            print("⚠️  ML модели не загружены")
        
        # Показываем статистику
        stats = indicator.get_performance_stats()
        print(f"\n📊 Загруженная статистика:")
        print(f"   Обучающих образцов: {stats.get('training_samples', 0)}")
        print(f"   Точность модели: {stats.get('model_accuracy', 0):.1f}%")
        
        return success or ml_success
        
    except Exception as e:
        print(f"❌ Ошибка загрузки: {e}")
        return False

if __name__ == "__main__":
    # Запускаем простой тест
    success = simple_training_test()
    
    if success:
        print(f"\n🎉 ТЕСТ ПРОШЕЛ УСПЕШНО!")
        
        # Тестируем загрузку
        load_success = test_model_loading()
        
        if load_success:
            print(f"🎉 ЗАГРУЗКА ТАКЖЕ РАБОТАЕТ!")
        else:
            print(f"⚠️  Проблемы с загрузкой")
    else:
        print(f"\n❌ ТЕСТ НЕ ПРОШЕЛ")
